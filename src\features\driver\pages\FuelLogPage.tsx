import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Fuel, Camera, MapPin, Receipt } from 'lucide-react';
import { useNotifications } from '@/hooks/useNotifications';
import { useAppDispatch } from '@/hooks/redux';
import { openModal } from '@/store/slices/uiSlice';

interface FuelLogData {
  date: string;
  time: string;
  odometer: string;
  liters: string;
  costPerLiter: string;
  totalCost: string;
  fuelType: string;
  station: string;
  location: string;
  receiptPhoto: File | null;
  notes: string;
}

const FuelLogPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { showSuccess, showError, showWarning } = useNotifications();

  const [formData, setFormData] = useState<FuelLogData>({
    date: new Date().toISOString().split('T')[0],
    time: new Date().toTimeString().slice(0, 5),
    odometer: '',
    liters: '',
    costPerLiter: '',
    totalCost: '',
    fuelType: 'petrol',
    station: '',
    location: '',
    receiptPhoto: null,
    notes: ''
  });

  const fuelTypes = [
    { id: 'petrol', name: 'Petrol (Unleaded)', icon: '⛽' },
    { id: 'diesel', name: 'Diesel', icon: '🚛' },
    { id: 'premium', name: 'Premium Petrol', icon: '⭐' }
  ];

  const commonStations = [
    'Shell',
    'BP',
    'Engen',
    'Sasol',
    'Caltex',
    'Total',
    'Other'
  ];

  // Auto-calculate total cost when liters or cost per liter changes
  useEffect(() => {
    if (formData.liters && formData.costPerLiter) {
      const total = (parseFloat(formData.liters) * parseFloat(formData.costPerLiter)).toFixed(2);
      setFormData(prev => ({ ...prev, totalCost: total }));
    }
  }, [formData.liters, formData.costPerLiter]);

  // Auto-calculate validation
  useEffect(() => {
    if (formData.liters && formData.costPerLiter) {
      const calculated = (parseFloat(formData.liters) * parseFloat(formData.costPerLiter)).toFixed(2);
      const entered = parseFloat(formData.totalCost);
      
      if (Math.abs(parseFloat(calculated) - entered) > 0.50) {
        showWarning('Calculation Mismatch', 'Total cost doesn\'t match liters × price per liter');
      }
    }
  }, [formData.liters, formData.costPerLiter, formData.totalCost]);

  const handleReceiptPhoto = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      
      // Check file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        showError('File Too Large', 'Receipt photo must be less than 5MB');
        return;
      }
      
      setFormData(prev => ({ ...prev, receiptPhoto: file }));
      showSuccess('Photo Added', 'Receipt photo uploaded successfully');
    }
  };

  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          setFormData(prev => ({
            ...prev,
            location: `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`
          }));
        },
        (error) => {
          console.error('Error getting location:', error);
          alert('Could not get current location. Please enter manually.');
        }
      );
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation
    if (!formData.odometer || parseFloat(formData.odometer) <= 0) {
      showError('Validation Error', 'Please enter a valid odometer reading');
      return;
    }

    if (!formData.liters || parseFloat(formData.liters) <= 0) {
      showError('Validation Error', 'Please enter a valid fuel amount');
      return;
    }

    if (!formData.totalCost || parseFloat(formData.totalCost) <= 0) {
      showError('Validation Error', 'Please enter a valid total cost');
      return;
    }

    // Check for unusually high fuel amounts
    const liters = parseFloat(formData.liters);
    if (liters > 80) {
      showWarning('High Fuel Amount', 'This seems like a large fuel purchase. Please verify the amount.');
    }

    // Check for unusually high cost per liter
    const costPerLiter = parseFloat(formData.costPerLiter);
    if (costPerLiter > 25) {
      showWarning('High Fuel Price', 'This fuel price seems unusually high. Please verify.');
    }

    // Show confirmation modal
    dispatch(openModal({
      id: 'confirm-fuel-log',
      type: 'confirm',
      props: {
        title: 'Confirm Fuel Log',
        message: `Log ${formData.liters}L of ${fuelTypes.find(t => t.id === formData.fuelType)?.name} for R${formData.totalCost}?`,
        confirmText: 'Save Log',
        cancelText: 'Cancel',
        onConfirm: () => {
          console.log('Fuel log submitted:', formData);
          showSuccess('Fuel Log Saved', 'Your fuel purchase has been recorded successfully');
          navigate('/driver/dashboard');
        }
      }
    }));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-blue-600 text-white p-4">
        <div className="flex items-center space-x-3">
          <button onClick={() => navigate(-1)} className="p-1">
            <ArrowLeft className="h-6 w-6" />
          </button>
          <Fuel className="h-6 w-6" />
          <h1 className="text-xl font-bold">Log Fuel Purchase</h1>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="p-4 space-y-6">
        {/* Date & Time */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Date
            </label>
            <input
              type="date"
              value={formData.date}
              onChange={(e) => setFormData({ ...formData, date: e.target.value })}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Time
            </label>
            <input
              type="time"
              value={formData.time}
              onChange={(e) => setFormData({ ...formData, time: e.target.value })}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>
        </div>

        {/* Odometer Reading */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Current Odometer Reading (km)
          </label>
          <input
            type="number"
            value={formData.odometer}
            onChange={(e) => setFormData({ ...formData, odometer: e.target.value })}
            placeholder="e.g., 45230"
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            required
          />
        </div>

        {/* Fuel Type */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Fuel Type
          </label>
          <div className="grid grid-cols-1 gap-2">
            {fuelTypes.map((type) => (
              <button
                key={type.id}
                type="button"
                onClick={() => setFormData({ ...formData, fuelType: type.id })}
                className={`p-3 rounded-lg border-2 text-left flex items-center space-x-3 ${
                  formData.fuelType === type.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 bg-white'
                }`}
              >
                <span className="text-xl">{type.icon}</span>
                <span className="font-medium">{type.name}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Fuel Details */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Liters
            </label>
            <input
              type="number"
              step="0.01"
              value={formData.liters}
              onChange={(e) => setFormData({ ...formData, liters: e.target.value })}
              placeholder="e.g., 45.50"
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Cost per Liter (R)
            </label>
            <input
              type="number"
              step="0.01"
              value={formData.costPerLiter}
              onChange={(e) => setFormData({ ...formData, costPerLiter: e.target.value })}
              placeholder="e.g., 21.50"
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>
        </div>

        {/* Total Cost (Auto-calculated) */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Total Cost (R)
          </label>
          <input
            type="number"
            step="0.01"
            value={formData.totalCost}
            onChange={(e) => setFormData({ ...formData, totalCost: e.target.value })}
            className="w-full p-3 border border-gray-300 rounded-lg bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Auto-calculated"
            required
          />
        </div>

        {/* Fuel Station */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Fuel Station
          </label>
          <select
            value={formData.station}
            onChange={(e) => setFormData({ ...formData, station: e.target.value })}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            required
          >
            <option value="">Select fuel station</option>
            {commonStations.map((station) => (
              <option key={station} value={station}>{station}</option>
            ))}
          </select>
        </div>

        {/* Location */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Location
          </label>
          <div className="flex space-x-2">
            <input
              type="text"
              value={formData.location}
              onChange={(e) => setFormData({ ...formData, location: e.target.value })}
              placeholder="Enter location or use GPS"
              className="flex-1 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
            <button
              type="button"
              onClick={getCurrentLocation}
              className="px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              <MapPin className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Receipt Photo */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Receipt Photo
          </label>
          <div className="space-y-3">
            <input
              type="file"
              accept="image/*"
              onChange={handleReceiptPhoto}
              className="hidden"
              id="receipt-upload"
            />
            <label
              htmlFor="receipt-upload"
              className="flex items-center justify-center w-full p-4 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-gray-400"
            >
              <div className="text-center">
                <Camera className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <span className="text-sm text-gray-600">
                  {formData.receiptPhoto ? 'Receipt photo added' : 'Tap to add receipt photo'}
                </span>
              </div>
            </label>

            {formData.receiptPhoto && (
              <div className="relative">
                <img
                  src={URL.createObjectURL(formData.receiptPhoto)}
                  alt="Receipt"
                  className="w-full h-40 object-cover rounded-lg"
                />
                <button
                  type="button"
                  onClick={() => setFormData(prev => ({ ...prev, receiptPhoto: null }))}
                  className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm"
                >
                  ×
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Notes */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Additional Notes (Optional)
          </label>
          <textarea
            value={formData.notes}
            onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
            placeholder="Any additional information about the fuel purchase..."
            rows={3}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* Summary Card */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-3">
            <Receipt className="h-5 w-5 text-blue-600" />
            <span className="font-medium text-blue-800">Fuel Purchase Summary</span>
          </div>
          <div className="space-y-1 text-sm text-blue-700">
            <p>• {formData.liters || '0'} liters of {fuelTypes.find(t => t.id === formData.fuelType)?.name || 'fuel'}</p>
            <p>• R{formData.costPerLiter || '0'} per liter</p>
            <p>• Total cost: R{formData.totalCost || '0'}</p>
            <p>• Odometer: {formData.odometer || '0'} km</p>
          </div>
        </div>

        {/* Submit Button */}
        <div className="pt-4">
          <button
            type="submit"
            className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Save Fuel Log
          </button>
        </div>
      </form>
    </div>
  );
};

export default FuelLogPage;
