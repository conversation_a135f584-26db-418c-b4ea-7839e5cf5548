import React, { useState } from 'react';
import { ArrowLeft, Camera, FileText, AlertTriangle, CheckCircle, Clock, Edit, Download } from 'lucide-react';
import { useNavigate, useParams } from 'react-router-dom';

const AccidentCaseDetailPage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [activeTab, setActiveTab] = useState('overview');

  // Mock data - replace with API call
  const accidentCase = {
    id: 'AC-001',
    vehicleId: 'V-001',
    vehicleReg: 'GP 123 ABC',
    vehicleMake: 'Toyota',
    vehicleModel: 'Hilux',
    accidentDate: '2025-01-14',
    accidentTime: '14:30',
    driverName: '<PERSON>',
    driverContact: '082 123 4567',
    location: 'N1 Highway, Pretoria',
    description: 'Rear-end collision, damage to rear bumper and tailgate',
    severity: 'Moderate',
    status: 'Assessed',
    priority: 'High',
    estimatedCost: 25000,
    assessorAssigned: '<PERSON>',
    photosCount: 8,
    insuranceClaim: 'INS-2025-001',
    reportedBy: '<PERSON>',
    reportDate: '2025-01-14',
    injuriesReported: false,
    policeReported: true,
    policeReportNumber: 'PR-2025-001',
    thirdPartyInvolved: false,
    weatherConditions: 'Clear',
    roadConditions: 'Dry'
  };

  const timeline = [
    { date: '2025-01-14 14:30', event: 'Accident occurred', status: 'completed' },
    { date: '2025-01-14 15:45', event: 'Accident reported by driver', status: 'completed' },
    { date: '2025-01-15 09:00', event: 'Assessor assigned (Sarah Johnson)', status: 'completed' },
    { date: '2025-01-15 14:00', event: 'On-site assessment completed', status: 'completed' },
    { date: '2025-01-16 10:00', event: 'Quotes requested from merchants', status: 'pending' },
    { date: 'TBD', event: 'Quote comparison and approval', status: 'upcoming' }
  ];

  const tabs = [
    { id: 'overview', label: 'Overview', icon: FileText },
    { id: 'photos', label: 'Photos', icon: Camera },
    { id: 'timeline', label: 'Timeline', icon: Clock },
    { id: 'assessment', label: 'Assessment', icon: CheckCircle }
  ];

  const handleStatusUpdate = (newStatus: string) => {
    console.log('Updating status to:', newStatus);
    // API call to update status
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <button 
            onClick={() => navigate('/maintenance/accidents')}
            className="p-2 hover:bg-gray-100 rounded-lg"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Accident Case {accidentCase.id}</h1>
            <p className="text-gray-600">{accidentCase.vehicleReg} - {accidentCase.vehicleMake} {accidentCase.vehicleModel}</p>
          </div>
        </div>
        <div className="flex gap-2">
          <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
            <Edit className="w-4 h-4" />
            Edit Case
          </button>
          <button className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
            <Download className="w-4 h-4" />
            Export Report
          </button>
        </div>
      </div>

      {/* Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow-sm border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Status</p>
              <p className="text-lg font-semibold text-gray-900">{accidentCase.status}</p>
            </div>
            <CheckCircle className="w-8 h-8 text-blue-500" />
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Severity</p>
              <p className="text-lg font-semibold text-orange-600">{accidentCase.severity}</p>
            </div>
            <AlertTriangle className="w-8 h-8 text-orange-500" />
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Estimated Cost</p>
              <p className="text-lg font-semibold text-gray-900">R {accidentCase.estimatedCost?.toLocaleString()}</p>
            </div>
            <FileText className="w-8 h-8 text-green-500" />
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Photos</p>
              <p className="text-lg font-semibold text-gray-900">{accidentCase.photosCount}</p>
            </div>
            <Camera className="w-8 h-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  {tab.label}
                </button>
              );
            })}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Accident Details */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Accident Details</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Date & Time:</span>
                    <span className="font-medium">{new Date(accidentCase.accidentDate).toLocaleDateString()} at {accidentCase.accidentTime}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Location:</span>
                    <span className="font-medium">{accidentCase.location}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Weather:</span>
                    <span className="font-medium">{accidentCase.weatherConditions}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Road Conditions:</span>
                    <span className="font-medium">{accidentCase.roadConditions}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Police Report:</span>
                    <span className="font-medium">{accidentCase.policeReported ? accidentCase.policeReportNumber : 'No'}</span>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Description</h4>
                  <p className="text-gray-700">{accidentCase.description}</p>
                </div>
              </div>

              {/* Driver & Vehicle Info */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Driver & Vehicle</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Driver:</span>
                    <span className="font-medium">{accidentCase.driverName}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Contact:</span>
                    <span className="font-medium">{accidentCase.driverContact}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Vehicle:</span>
                    <span className="font-medium">{accidentCase.vehicleReg}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Make/Model:</span>
                    <span className="font-medium">{accidentCase.vehicleMake} {accidentCase.vehicleModel}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Assessor:</span>
                    <span className="font-medium">{accidentCase.assessorAssigned || 'Not assigned'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Insurance Claim:</span>
                    <span className="font-medium">{accidentCase.insuranceClaim || 'Not filed'}</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'timeline' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Case Timeline</h3>
              <div className="space-y-4">
                {timeline.map((item, index) => (
                  <div key={index} className="flex items-start gap-4">
                    <div className={`w-3 h-3 rounded-full mt-2 ${
                      item.status === 'completed' ? 'bg-green-500' :
                      item.status === 'pending' ? 'bg-yellow-500' : 'bg-gray-300'
                    }`} />
                    <div className="flex-1">
                      <p className="font-medium text-gray-900">{item.event}</p>
                      <p className="text-sm text-gray-600">{item.date}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'photos' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Accident Photos</h3>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {Array.from({ length: accidentCase.photosCount }, (_, i) => (
                  <div key={i} className="aspect-square bg-gray-200 rounded-lg flex items-center justify-center">
                    <Camera className="w-8 h-8 text-gray-400" />
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'assessment' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Assessment Report</h3>
              <div className="bg-gray-50 rounded-lg p-4">
                <p className="text-gray-600">Assessment completed by {accidentCase.assessorAssigned}</p>
                <p className="text-sm text-gray-500 mt-1">Estimated repair cost: R {accidentCase.estimatedCost?.toLocaleString()}</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AccidentCaseDetailPage;