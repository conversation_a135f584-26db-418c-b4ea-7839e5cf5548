import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Plus, 
  Search, 
  Filter, 
  Star, 
  MapPin, 
  Phone, 
  Mail,
  AlertTriangle,
  CheckCircle,
  Users,
  Eye,
  Edit
} from 'lucide-react';
import { useGetVendorsQuery } from '@/store/api/vendorApi';
import { useDispatch, useSelector } from 'react-redux';
import { openModal, closeModal } from '@/store/slices/uiSlice';
import { RootState } from '@/store';
import Modal from '@/components/ui/Modal';
import { useNotifications } from '@/hooks/useNotifications';
import type { Vendor } from '@/types/vendor';
import { mockVendors } from '@/utils/mockData';

export default function VendorListPage() {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { modals } = useSelector((state: RootState) => state.ui);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSpecialization, setSelectedSpecialization] = useState<'all' | 'bodyshop' | 'paintshop' | 'mechanic' | 'tireshop'>('all');
  const [selectedStatus, setSelectedStatus] = useState<'all' | 'active' | 'inactive' | 'suspended'>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 9;
  const [selectedVendors, setSelectedVendors] = useState<string[]>([]);
  const [selectedVendor, setSelectedVendor] = useState<Vendor | null>(null);
  const { showSuccess, showError } = useNotifications();
  const [sortBy, setSortBy] = useState<'name' | 'rating' | 'totalJobs' | 'averageCost' | 'averageCompletionTime'>('name');
  
  // Fix the status filter type
  const [filters, setFilters] = useState({
    search: '',
    status: '' as 'active' | 'inactive' | 'suspended',
    services: [],
    city: ''
  });

  // Use mock data instead of API
  const vendors = mockVendors;
  const isLoading = false;
  const error = null;

  // Remove or comment out the API call
  // const { data, isLoading, error } = useGetVendorsQuery({
  //   page: currentPage,
  //   limit: itemsPerPage,
  //   filters: {
  //     search: searchTerm,
  //     status: selectedStatus !== 'all' ? selectedStatus : undefined,
  //     services: selectedSpecialization !== 'all' ? selectedSpecialization : undefined,
  //   }
  // });

  const totalVendors = vendors.length;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading vendors...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-red-500">Failed to load vendors</div>
      </div>
    );
  }

  const filteredVendors = vendors.filter(vendor => {
    const matchesSearch = vendor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         vendor.contactPerson.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         vendor.city.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesSpecialization = selectedSpecialization === 'all' || vendor.services.includes(selectedSpecialization);
    const matchesStatus = selectedStatus === 'all' || vendor.status === selectedStatus;
    return matchesSearch && matchesSpecialization && matchesStatus;
  });

  const totalPages = Math.ceil(filteredVendors.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedVendors = filteredVendors.slice(startIndex, startIndex + itemsPerPage);

  const getStatusBadge = (status: string) => {
    const styles = {
      active: 'bg-green-100 text-green-800',
      inactive: 'bg-gray-100 text-gray-800',
      suspended: 'bg-red-100 text-red-800',
      pending_approval: 'bg-yellow-100 text-yellow-800',
    };
    return styles[status as keyof typeof styles] || 'bg-gray-100 text-gray-800';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'pending_approval': return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      default: return <AlertTriangle className="h-4 w-4 text-gray-600" />;
    }
  };

  const getBBBEEColor = (level: number) => {
    if (level <= 2) return 'bg-green-100 text-green-800';
    if (level <= 4) return 'bg-yellow-100 text-yellow-800';
    return 'bg-orange-100 text-orange-800';
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  const allSpecializations = Array.from(new Set(vendors.flatMap(vendor => vendor.services)));

  const handleBulkDelete = () => {
    if (selectedVendors.length === 0) return;
    
    dispatch(openModal({
      id: 'bulk-delete-vendors',
      type: 'confirm',
      props: {
        count: selectedVendors.length
      }
    }));
  };

  const handleBulkDeleteConfirm = async () => {
    try {
      // Implement bulk delete logic
      showSuccess(`${selectedVendors.length} vendors deleted successfully!`);
      setSelectedVendors([]);
      dispatch(closeModal('bulk-delete-vendors'));
    } catch (error) {
      showError('Failed to delete vendors. Please try again.');
    }
  };

  const handleViewVendor = (vendor: Vendor) => {
    navigate(`/vendors/${vendor.id}`);
  };

  const handleCloseModal = () => {
    dispatch(closeModal('vendor-details'));
    setSelectedVendor(null);
  };

  const renderServices = (services: string[]) => {
    return services.slice(0, 2).map((service, index) => (
      <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-md">
        {service}
      </span>
    ));
  };

  return (
    <div className="container mx-auto px-6 py-8 space-y-6 max-w-7xl">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Vendor Management</h1>
          <p className="text-gray-600">Manage service providers and suppliers</p>
        </div>
        <Button 
          onClick={() => navigate('/vendors/add')}
          className="bg-blue-700 hover:bg-blue-800"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Vendor
        </Button>
      </div>

      {/* Filters and Search */}
      <Card className="w-full">
        <CardContent className="p-6">
          <div className="flex flex-col lg:flex-row gap-4 w-full">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search vendors by name, contact person, or city..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-full"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={selectedSpecialization}
                onChange={(e) => setSelectedSpecialization(e.target.value as 'all' | 'bodyshop' | 'paintshop' | 'mechanic' | 'tireshop')}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-700"
              >
                <option value="all">All Services</option>
                <option value="bodyshop">Body Shop</option>
                <option value="paintshop">Paint Shop</option>
                <option value="mechanic">Mechanic</option>
                <option value="tireshop">Tire Shop</option>
              </select>
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value as 'all' | 'active' | 'inactive' | 'suspended')}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-700"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="suspended">Suspended</option>
                <option value="pending_approval">Pending Approval</option>
              </select>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as 'name' | 'rating' | 'totalJobs' | 'averageCost' | 'averageCompletionTime')}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-700"
              >
                <option value="name">Sort by Name</option>
                <option value="rating">Sort by Rating</option>
                <option value="totalJobs">Sort by Jobs</option>
                <option value="averageCost">Sort by Cost</option>
                <option value="averageCompletionTime">Sort by Speed</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Vendor Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {paginatedVendors.map((vendor) => (
          <Card key={vendor.id} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-2">
                  <Users className="h-5 w-5 text-blue-700" />
                  <CardTitle className="text-lg">{vendor.name}</CardTitle>
                </div>
                <div className="flex items-center space-x-1">
                  {getStatusIcon(vendor.status)}
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Users className="h-4 w-4" />
                  <span>{vendor.contactPerson}</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Mail className="h-4 w-4" />
                  <span>{vendor.email}</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Phone className="h-4 w-4" />
                  <span>{vendor.phone}</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <MapPin className="h-4 w-4" />
                  <span>{vendor.city}, {vendor.province}</span>
                </div>
              </div>

              <div className="flex space-x-2 pt-2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="flex-1"
                  onClick={() => handleViewVendor(vendor)}
                >
                  <Eye className="h-4 w-4 mr-1" />
                  View
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="flex-1"
                  onClick={() => navigate(`/vendors/${vendor.id}/edit`)}
                >
                  <Edit className="h-4 w-4 mr-1" />
                  Edit
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
          >
            Previous
          </Button>
          
          <div className="flex space-x-1">
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <Button
                key={page}
                variant={currentPage === page ? "default" : "outline"}
                size="sm"
                onClick={() => setCurrentPage(page)}
                className="w-8 h-8 p-0"
              >
                {page}
              </Button>
            ))}
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  );
}








