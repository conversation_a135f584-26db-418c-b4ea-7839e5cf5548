Based on the **PRD** and the **user role breakdown**, here's a comprehensive mapping of each **MVP screen** with **detailed components per user**, ensuring alignment with the MVP checklist features (Category A, A1, C, C1) and core personas.

---

## 🔐 **Part 0: Universal Screens (All Users)**

### 1. **Login Screen**

* Fields: Username, Password
* Features: Forgot password link, reCAPTCHA, MFA support

### 2. **Forgot/Reset Password Screen**

* Email input
* OTP or reset link
* New password validation

### 3. **New User Account Setup**

* Temporary token validation
* Password creation
* Terms of use acceptance

### 4. **Notification Preferences**

* Toggle system/email/SMS for:

  * Job status updates
  * Approvals
  * Inspections
  * Alerts (e.g. speeding, downtime)

### 5. **Helpdesk Center**

* FAQ access
* Submit ticket (dropdown: issue category, description, attachment)
* Track ticket status

---

## 🧑‍💼 **Fleet Manager Screens**

### 1. **Fleet Manager Dashboard**

* KPIs: Vehicle uptime, budget utilization, vendor performance
* Alerts: Pending approvals, vehicle downtime, SLA breaches
* Quick links: Add Vehicle, View Merchant Quotes

### 2. **Vehicle List View**

* Table view with filters: status, location, due service
* Actions: View, Edit, Archive, Assign driver

### 3. **Vehicle Detail View**

* Sections:

  * Basic info (VIN, reg, make, year)
  * Service history
  * Warranty & documents
  * Maintenance schedule
  * Driver assignment

### 4. **Vehicle Onboarding Wizard**

* Step 1: Basic Info
* Step 2: Department & Cost Center
* Step 3: Maintenance schedule
* Step 4: Warranty details & Docs Upload

### 5. **Maintenance Request List**

* Columns: Vehicle, Request type, Status, Assigned Merchant, Due date
* Filters: Status, Merchant, Type (Scheduled, Unplanned)

### 6. **Quotation Comparison Screen**

* Table: Merchant, Price, ETA, Warranty, SLA Score
* Action: Accept/Reject, Comments
* OEM vs alternate parts view toggle

### 7. **Accident Case Management**

* Fields: Accident date, Driver involved, Photos
* Assessment form
* Quotation module with damage estimate engine

### 8. **Merchant Directory**

* Table: Merchant name, Services, Region, Score, Spend
* Actions: View profile, Rate merchant

### 9. **Merchant Profile View**

* Basic info, geo-coordinates, compliance docs
* Job history: timeline view
* Performance radar (turnaround time, quality score)

### 10. **Live Vehicle Tracking Map**

* Real-time map with:

  * Status (idle, moving, off)
  * Geo-fencing alerts
  * Filter: By vehicle/department

### 11. **Report Builder**

* Drag-and-drop report builder
* Templates: Spend by category, Vehicles due for service
* Schedule & export (CSV, PDF)

### 12. **Role & Permission Management**

* Role assignment per user
* Access matrix configuration
* Audit of permission changes

---

## 🚛 **Transport Officer Screens**

### 1. **Transport Officer Dashboard**

* Alerts: Vehicle overdue for service
* Widgets: Bookings today, Upcoming trips
* Action shortcuts: Initiate maintenance, Assign driver

### 2. **Maintenance Scheduling**

* Calendar view
* Drag vehicle into schedule slot
* Sync with vendor capacity

### 3. **Trip Log Viewer**

* Vehicle-based or driver-based filtering
* Data: Date, Route, Distance, Fuel, Duration

### 4. **Booking Calendar**

* View bookings by vehicle
* Approve/reject requests
* Notify driver

### 5. **Maintenance Request Form**

* Select vehicle
* Type of issue
* Photo upload
* Assign merchant (auto suggestion)

---

## 🚗 **Driver (Mobile App First)**

### 1. **Driver Dashboard**

* Current vehicle assigned
* Next booking
* Fuel status (last entry)

### 2. **Vehicle Booking Form**

* Fields: Destination, Reason, Date/time, Vehicle type
* Approval status tracker

### 3. **Pre-Trip Inspection Checklist**

* Tappable items: lights, tires, brakes, etc.
* Photo capture
* Signature box

### 4. **Issue Reporting Form**

* Dropdown: Breakdown, Minor fault, Accident
* Photo upload
* Location auto-capture

### 5. **Fuel Log Form**

* Input: Liters, Cost, Station
* Odometer reading
* Receipt image upload

### 6. **Trip History**

* List view
* Filter by date
* Export/share PDF option

---

## 💰 **Finance Officer Screens**

### 1. **Finance Dashboard**

* Widgets: Pending payments, Budget vs Spend
* Alerts: Approvals overdue

### 2. **Invoice Dashboard / Queue**

* Columns: Merchant, Invoice No, Amount, Status
* Actions: Approve, Hold, Flag

### 3. **Invoice Detail View**

* Invoice PDF viewer
* Linked work order preview
* Approval trail display

### 4. **Payment Status Tracker**

* Searchable by invoice/vendor
* Status: Pending, Approved, Paid, Rejected

### 5. **Budget Management**

* Input: Department, Allocation, Period
* Live budget tracking graph

---

## 🧰 **Vendor / Merchant Portal**

### 1. **Merchant Dashboard**

* New job notifications
* Stats: Total jobs, Avg rating, Outstanding invoices

### 2. **Work Order Queue**

* Filter by due date/status
* Accept/Decline buttons
* SLA timer countdown

### 3. **Quote Submission**

* Input: Labor, Parts, Duration, Warranty
* Upload files (quotes, supplier docs)
* Fraud detection suggestions

### 4. **Job Card View**

* Non-editable order summary
* Linked vehicle & contact

### 5. **Invoice Submission**

* Auto-filled from job card
* Editable price fields
* VAT/Tax confirmation

### 6. **Payment Tracker**

* Table: Invoice, Date, Status
* Bank details on file

---

## 🕵️‍♂️ **Inspector (Mobile App)**

### 1. **Inspector Dashboard**

* Scheduled inspections
* Priority flags: e.g. rework inspections

### 2. **Inspection Checklist Interface**

* Dynamic checklist: Pre or Post repair
* Toggle Pass/Fail
* Photo, notes, signature
* Location stamped

---

## 🧑‍💼 **Executive**

### 1. **Executive Dashboard**

* Visualizations:

  * Cost reduction trend
  * SLA compliance
  * Fleet health map
* Department filter
* Read-only access

---

## 📋 **Auditor**

### 1. **Audit Trail Log Viewer**

* Search by: User, Action, Date
* Immutable records
* Export logs (PDF, CSV)

### 2. **Compliance Reporting**

* Pre-built templates: e.g. PFMA, POPIA checks
* Download & schedule feature

### 3. **Data Export Config**

* Export builder
* Select tables: Vehicles, Quotes, Logs, Invoices
* Compliance banner for confirmation

---

## ✅ Summary

| Role              | Total Screens |
| ----------------- | ------------- |
| Fleet Manager     | 12            |
| Transport Officer | 5             |
| Driver            | 6 (Mobile)    |
| Finance Officer   | 5             |
| Vendor/Merchant   | 6             |
| Inspector         | 2 (Mobile)    |
| Executive         | 1             |
| Auditor           | 3             |
| All Users         | 5             |

---
