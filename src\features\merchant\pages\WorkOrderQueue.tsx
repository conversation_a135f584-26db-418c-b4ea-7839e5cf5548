import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  Car,
  MapPin,
  Calendar,
  Search,
  Filter,
  Eye,
  Check,
  X,
  Timer
} from 'lucide-react';
import { useNotifications } from '@/hooks/useNotifications';
import { useDispatch, useSelector } from 'react-redux';
import { openModal, closeModal } from '@/store/slices/uiSlice';
import { RootState } from '@/store';
import ConfirmDialog from '@/components/ui/ConfirmDialog';

interface WorkOrder {
  id: string;
  workOrderNumber: string;
  vehicle: {
    registration: string;
    make: string;
    model: string;
  };
  serviceType: string;
  priority: 'Low' | 'Medium' | 'High' | 'Urgent';
  status: 'New' | 'Accepted' | 'In Progress' | 'Completed';
  dueDate: string;
  location: string;
  estimatedHours: number;
  description: string;
  customer: {
    department: string;
    contactPerson: string;
    phone: string;
  };
  slaHours: number;
  timeRemaining: number;
}

const WorkOrderQueue: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { showSuccess, showError } = useNotifications();
  const { modals } = useSelector((state: RootState) => state.ui);
  
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [selectedWorkOrder, setSelectedWorkOrder] = useState<string | null>(null);

  // Mock data
  const workOrders: WorkOrder[] = [
    {
      id: 'WO-2025-001',
      workOrderNumber: 'WO-2025-001',
      vehicle: {
        registration: 'GP123ABC',
        make: 'Toyota',
        model: 'Hilux'
      },
      serviceType: 'Brake Service',
      priority: 'High',
      status: 'New',
      dueDate: '2025-01-15',
      location: 'Pretoria Central',
      estimatedHours: 4,
      description: 'Replace brake pads and check brake fluid levels',
      customer: {
        department: 'Department of Health',
        contactPerson: 'Dr. Sarah Johnson',
        phone: '************'
      },
      slaHours: 48,
      timeRemaining: 36
    },
    {
      id: 'WO-2025-002',
      workOrderNumber: 'WO-2025-002',
      vehicle: {
        registration: 'GP456DEF',
        make: 'Ford',
        model: 'Ranger'
      },
      serviceType: 'Engine Diagnostics',
      priority: 'Medium',
      status: 'Accepted',
      dueDate: '2025-01-18',
      location: 'Centurion',
      estimatedHours: 6,
      description: 'Diagnose engine warning light and performance issues',
      customer: {
        department: 'Department of Transport',
        contactPerson: 'Mr. John Smith',
        phone: '************'
      },
      slaHours: 72,
      timeRemaining: 60
    }
  ];

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'Urgent': return 'bg-red-100 text-red-800';
      case 'High': return 'bg-orange-100 text-orange-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'Low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'New': return 'bg-blue-100 text-blue-800';
      case 'Accepted': return 'bg-green-100 text-green-800';
      case 'In Progress': return 'bg-yellow-100 text-yellow-800';
      case 'Completed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleAcceptJob = (workOrderId: string) => {
    setSelectedWorkOrder(workOrderId);
    dispatch(openModal({
      id: 'accept-work-order',
      type: 'confirm',
      props: {
        title: 'Accept Work Order',
        message: 'Are you sure you want to accept this work order? This action cannot be undone.',
        confirmText: 'Accept',
        cancelText: 'Cancel',
        variant: 'success'
      }
    }));
  };

  const handleDeclineJob = (workOrderId: string) => {
    setSelectedWorkOrder(workOrderId);
    dispatch(openModal({
      id: 'decline-work-order',
      type: 'confirm',
      props: {
        title: 'Decline Work Order',
        message: 'Are you sure you want to decline this work order? Please provide a reason.',
        confirmText: 'Decline',
        cancelText: 'Cancel',
        variant: 'destructive',
        requiresReason: true
      }
    }));
  };

  const handleConfirmAccept = async () => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      showSuccess('Work order accepted successfully!');
      dispatch(closeModal('accept-work-order'));
      setSelectedWorkOrder(null);
    } catch (error) {
      showError('Failed to accept work order. Please try again.');
    }
  };

  const handleConfirmDecline = async (reason?: string) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      showSuccess('Work order declined successfully.');
      dispatch(closeModal('decline-work-order'));
      setSelectedWorkOrder(null);
    } catch (error) {
      showError('Failed to decline work order. Please try again.');
    }
  };

  const handleViewDetails = (workOrderId: string) => {
    navigate(`/merchant/work-orders/${workOrderId}`);
  };

  const filteredOrders = workOrders.filter(order => {
    const matchesSearch = order.workOrderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.vehicle.registration.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.serviceType.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || order.status === statusFilter;
    const matchesPriority = priorityFilter === 'all' || order.priority === priorityFilter;
    
    return matchesSearch && matchesStatus && matchesPriority;
  });

  return (
    <div className="container mx-auto px-6 py-8 space-y-6 max-w-7xl">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Work Order Queue</h1>
          <p className="text-gray-600">Manage assigned work orders and new job requests</p>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">New Orders</p>
                <p className="text-2xl font-bold text-gray-900">
                  {workOrders.filter(w => w.status === 'New').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">In Progress</p>
                <p className="text-2xl font-bold text-gray-900">
                  {workOrders.filter(w => w.status === 'In Progress').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <AlertTriangle className="h-8 w-8 text-orange-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Urgent</p>
                <p className="text-2xl font-bold text-gray-900">
                  {workOrders.filter(w => w.priority === 'Urgent').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Timer className="h-8 w-8 text-red-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Due Today</p>
                <p className="text-2xl font-bold text-gray-900">2</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search by work order, vehicle, or service type..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-40">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="New">New</SelectItem>
                  <SelectItem value="Accepted">Accepted</SelectItem>
                  <SelectItem value="In Progress">In Progress</SelectItem>
                  <SelectItem value="Completed">Completed</SelectItem>
                </SelectContent>
              </Select>

              <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Priority</SelectItem>
                  <SelectItem value="Urgent">Urgent</SelectItem>
                  <SelectItem value="High">High</SelectItem>
                  <SelectItem value="Medium">Medium</SelectItem>
                  <SelectItem value="Low">Low</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Work Orders List */}
      <div className="space-y-4">
        {filteredOrders.map((order) => (
          <Card key={order.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-4">
                    <div>
                      <div className="flex items-center space-x-2 mb-2">
                        <h3 className="text-lg font-semibold">{order.workOrderNumber}</h3>
                        <Badge className={getPriorityColor(order.priority)}>
                          {order.priority}
                        </Badge>
                        <Badge className={getStatusColor(order.status)}>
                          {order.status}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-1">{order.serviceType}</p>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <div className="flex items-center">
                          <Car className="h-4 w-4 mr-1" />
                          {order.vehicle.make} {order.vehicle.model} - {order.vehicle.registration}
                        </div>
                        <div className="flex items-center">
                          <MapPin className="h-4 w-4 mr-1" />
                          {order.location}
                        </div>
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-1" />
                          Due: {order.dueDate}
                        </div>
                        <div className="flex items-center">
                          <Clock className="h-4 w-4 mr-1" />
                          {order.timeRemaining}h remaining
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex flex-col space-y-2 ml-6">
                  <Button size="sm" variant="outline" onClick={() => handleViewDetails(order.id)}>
                    <Eye className="h-4 w-4 mr-2" />
                    View Details
                  </Button>
                  {order.status === 'New' && (
                    <div className="flex space-x-2">
                      <Button size="sm" onClick={() => handleAcceptJob(order.id)}>
                        <Check className="h-4 w-4 mr-2" />
                        Accept
                      </Button>
                      <Button size="sm" variant="outline" onClick={() => handleDeclineJob(order.id)}>
                        <X className="h-4 w-4 mr-2" />
                        Decline
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Modals */}
      {modals['accept-work-order' as keyof typeof modals] && (
        <ConfirmDialog
          isOpen={true}
          onClose={() => dispatch(closeModal('accept-work-order'))}
          onConfirm={handleConfirmAccept}
          title="Accept Work Order"
          message="Are you sure you want to accept this work order? This action cannot be undone."
          type="success"
          confirmText="Accept"
          cancelText="Cancel"
        />
      )}

      {modals['decline-work-order' as keyof typeof modals] && (
        <ConfirmDialog
          isOpen={true}
          onClose={() => dispatch(closeModal('decline-work-order'))}
          onConfirm={handleConfirmDecline}
          title="Decline Work Order"
          message="Are you sure you want to decline this work order? Please provide a reason."
          type="danger"
          confirmText="Decline"
          cancelText="Cancel"
        />
      )}
    </div>
  );
};

export default WorkOrderQueue;





