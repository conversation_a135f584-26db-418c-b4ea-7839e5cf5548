{"global": {"colors": {"primary": {"50": {"value": "#eff6ff", "type": "color"}, "100": {"value": "#dbeafe", "type": "color"}, "500": {"value": "#3b82f6", "type": "color"}, "600": {"value": "#2563eb", "type": "color"}, "700": {"value": "#1d4ed8", "type": "color"}, "800": {"value": "#1e40af", "type": "color"}}, "gray": {"50": {"value": "#f9fafb", "type": "color"}, "100": {"value": "#f3f4f6", "type": "color"}, "200": {"value": "#e5e7eb", "type": "color"}, "300": {"value": "#d1d5db", "type": "color"}, "400": {"value": "#9ca3af", "type": "color"}, "500": {"value": "#6b7280", "type": "color"}, "600": {"value": "#4b5563", "type": "color"}, "700": {"value": "#374151", "type": "color"}, "800": {"value": "#1f2937", "type": "color"}, "900": {"value": "#111827", "type": "color"}}, "success": {"50": {"value": "#dcfce7", "type": "color"}, "100": {"value": "#bbf7d0", "type": "color"}, "500": {"value": "#10b981", "type": "color"}, "600": {"value": "#059669", "type": "color"}, "700": {"value": "#047857", "type": "color"}, "800": {"value": "#166534", "type": "color"}}, "warning": {"50": {"value": "#fef3c7", "type": "color"}, "100": {"value": "#fde68a", "type": "color"}, "500": {"value": "#f59e0b", "type": "color"}, "600": {"value": "#d97706", "type": "color"}, "700": {"value": "#b45309", "type": "color"}, "800": {"value": "#92400e", "type": "color"}}, "error": {"50": {"value": "#fee2e2", "type": "color"}, "100": {"value": "#fecaca", "type": "color"}, "500": {"value": "#ef4444", "type": "color"}, "600": {"value": "#dc2626", "type": "color"}, "700": {"value": "#b91c1c", "type": "color"}, "800": {"value": "#991b1b", "type": "color"}}, "orange": {"50": {"value": "#fff7ed", "type": "color"}, "100": {"value": "#ffedd5", "type": "color"}, "500": {"value": "#f97316", "type": "color"}, "600": {"value": "#ea580c", "type": "color"}, "700": {"value": "#c2410c", "type": "color"}, "800": {"value": "#9a3412", "type": "color"}}, "white": {"value": "#ffffff", "type": "color"}, "black": {"value": "#000000", "type": "color"}}, "typography": {"fontFamilies": {"primary": {"value": "<PERSON><PERSON><PERSON>, ui-sans-serif, system-ui, sans-serif", "type": "fontFamilies"}}, "fontWeights": {"normal": {"value": "400", "type": "fontWeights"}, "medium": {"value": "500", "type": "fontWeights"}, "semibold": {"value": "600", "type": "fontWeights"}, "bold": {"value": "700", "type": "fontWeights"}}, "fontSize": {"xs": {"value": "12", "type": "fontSizes"}, "sm": {"value": "14", "type": "fontSizes"}, "base": {"value": "16", "type": "fontSizes"}, "lg": {"value": "18", "type": "fontSizes"}, "xl": {"value": "20", "type": "fontSizes"}, "2xl": {"value": "24", "type": "fontSizes"}, "3xl": {"value": "30", "type": "fontSizes"}}, "lineHeight": {"tight": {"value": "20", "type": "lineHeights"}, "normal": {"value": "24", "type": "lineHeights"}, "relaxed": {"value": "28", "type": "lineHeights"}, "loose": {"value": "32", "type": "lineHeights"}}}, "spacing": {"0": {"value": "0", "type": "spacing"}, "1": {"value": "4", "type": "spacing"}, "2": {"value": "8", "type": "spacing"}, "3": {"value": "12", "type": "spacing"}, "4": {"value": "16", "type": "spacing"}, "5": {"value": "20", "type": "spacing"}, "6": {"value": "24", "type": "spacing"}, "8": {"value": "32", "type": "spacing"}, "10": {"value": "40", "type": "spacing"}, "12": {"value": "48", "type": "spacing"}, "16": {"value": "64", "type": "spacing"}, "20": {"value": "80", "type": "spacing"}, "24": {"value": "96", "type": "spacing"}, "32": {"value": "128", "type": "spacing"}}, "borderRadius": {"none": {"value": "0", "type": "borderRadius"}, "sm": {"value": "4", "type": "borderRadius"}, "base": {"value": "6", "type": "borderRadius"}, "md": {"value": "8", "type": "borderRadius"}, "lg": {"value": "12", "type": "borderRadius"}, "xl": {"value": "16", "type": "borderRadius"}, "full": {"value": "9999", "type": "borderRadius"}}, "borderWidth": {"0": {"value": "0", "type": "borderWidth"}, "1": {"value": "1", "type": "borderWidth"}, "2": {"value": "2", "type": "borderWidth"}, "4": {"value": "4", "type": "borderWidth"}}, "boxShadow": {"sm": {"value": [{"x": "0", "y": "1", "blur": "2", "spread": "0", "color": "rgba(0, 0, 0, 0.05)", "type": "dropShadow"}], "type": "boxShadow"}, "base": {"value": [{"x": "0", "y": "1", "blur": "3", "spread": "0", "color": "rgba(0, 0, 0, 0.1)", "type": "dropShadow"}, {"x": "0", "y": "1", "blur": "2", "spread": "-1", "color": "rgba(0, 0, 0, 0.1)", "type": "dropShadow"}], "type": "boxShadow"}, "md": {"value": [{"x": "0", "y": "4", "blur": "6", "spread": "-1", "color": "rgba(0, 0, 0, 0.1)", "type": "dropShadow"}, {"x": "0", "y": "2", "blur": "4", "spread": "-2", "color": "rgba(0, 0, 0, 0.1)", "type": "dropShadow"}], "type": "boxShadow"}, "lg": {"value": [{"x": "0", "y": "10", "blur": "15", "spread": "-3", "color": "rgba(0, 0, 0, 0.1)", "type": "dropShadow"}, {"x": "0", "y": "4", "blur": "6", "spread": "-4", "color": "rgba(0, 0, 0, 0.1)", "type": "dropShadow"}], "type": "boxShadow"}}}, "semantic": {"colors": {"text": {"primary": {"value": "{global.colors.gray.900}", "type": "color"}, "secondary": {"value": "{global.colors.gray.600}", "type": "color"}, "muted": {"value": "{global.colors.gray.500}", "type": "color"}, "white": {"value": "{global.colors.white}", "type": "color"}}, "background": {"primary": {"value": "{global.colors.white}", "type": "color"}, "secondary": {"value": "{global.colors.gray.50}", "type": "color"}, "muted": {"value": "{global.colors.gray.100}", "type": "color"}}, "border": {"default": {"value": "{global.colors.gray.200}", "type": "color"}, "muted": {"value": "{global.colors.gray.100}", "type": "color"}}, "brand": {"primary": {"value": "{global.colors.primary.600}", "type": "color"}, "primaryHover": {"value": "{global.colors.primary.700}", "type": "color"}, "light": {"value": "{global.colors.primary.50}", "type": "color"}}, "status": {"success": {"value": "{global.colors.success.600}", "type": "color"}, "successLight": {"value": "{global.colors.success.50}", "type": "color"}, "warning": {"value": "{global.colors.warning.600}", "type": "color"}, "warningLight": {"value": "{global.colors.warning.50}", "type": "color"}, "error": {"value": "{global.colors.error.600}", "type": "color"}, "errorLight": {"value": "{global.colors.error.50}", "type": "color"}}}}, "components": {"button": {"primary": {"backgroundColor": {"value": "{semantic.colors.brand.primary}", "type": "color"}, "color": {"value": "{semantic.colors.text.white}", "type": "color"}, "borderRadius": {"value": "{global.borderRadius.base}", "type": "borderRadius"}, "paddingVertical": {"value": "{global.spacing.2}", "type": "spacing"}, "paddingHorizontal": {"value": "{global.spacing.4}", "type": "spacing"}, "fontSize": {"value": "{global.typography.fontSize.sm}", "type": "fontSizes"}, "fontWeight": {"value": "{global.typography.fontWeights.medium}", "type": "fontWeights"}, "hover": {"backgroundColor": {"value": "{semantic.colors.brand.primaryHover}", "type": "color"}}}, "secondary": {"backgroundColor": {"value": "{semantic.colors.background.muted}", "type": "color"}, "color": {"value": "{semantic.colors.text.primary}", "type": "color"}, "borderRadius": {"value": "{global.borderRadius.base}", "type": "borderRadius"}, "paddingVertical": {"value": "{global.spacing.2}", "type": "spacing"}, "paddingHorizontal": {"value": "{global.spacing.4}", "type": "spacing"}, "fontSize": {"value": "{global.typography.fontSize.sm}", "type": "fontSizes"}, "fontWeight": {"value": "{global.typography.fontWeights.medium}", "type": "fontWeights"}}, "outline": {"backgroundColor": {"value": "{semantic.colors.background.primary}", "type": "color"}, "color": {"value": "{semantic.colors.text.primary}", "type": "color"}, "borderColor": {"value": "{semantic.colors.border.default}", "type": "color"}, "borderWidth": {"value": "{global.borderWidth.1}", "type": "borderWidth"}, "borderRadius": {"value": "{global.borderRadius.base}", "type": "borderRadius"}, "paddingVertical": {"value": "{global.spacing.2}", "type": "spacing"}, "paddingHorizontal": {"value": "{global.spacing.4}", "type": "spacing"}, "fontSize": {"value": "{global.typography.fontSize.sm}", "type": "fontSizes"}, "fontWeight": {"value": "{global.typography.fontWeights.medium}", "type": "fontWeights"}}}, "card": {"default": {"backgroundColor": {"value": "{semantic.colors.background.primary}", "type": "color"}, "borderColor": {"value": "{semantic.colors.border.default}", "type": "color"}, "borderWidth": {"value": "{global.borderWidth.1}", "type": "borderWidth"}, "borderRadius": {"value": "{global.borderRadius.md}", "type": "borderRadius"}, "boxShadow": {"value": "{global.boxShadow.sm}", "type": "boxShadow"}, "padding": {"value": "{global.spacing.6}", "type": "spacing"}}}, "input": {"default": {"backgroundColor": {"value": "{semantic.colors.background.primary}", "type": "color"}, "borderColor": {"value": "{semantic.colors.border.default}", "type": "color"}, "borderWidth": {"value": "{global.borderWidth.1}", "type": "borderWidth"}, "borderRadius": {"value": "{global.borderRadius.base}", "type": "borderRadius"}, "paddingVertical": {"value": "{global.spacing.2}", "type": "spacing"}, "paddingHorizontal": {"value": "{global.spacing.3}", "type": "spacing"}, "fontSize": {"value": "{global.typography.fontSize.sm}", "type": "fontSizes"}, "height": {"value": "{global.spacing.10}", "type": "spacing"}, "focus": {"borderColor": {"value": "{semantic.colors.brand.primary}", "type": "color"}, "boxShadow": {"value": [{"x": "0", "y": "0", "blur": "0", "spread": "2", "color": "rgba(37, 99, 235, 0.2)", "type": "dropShadow"}], "type": "boxShadow"}}}}, "badge": {"success": {"backgroundColor": {"value": "{semantic.colors.status.successLight}", "type": "color"}, "color": {"value": "{global.colors.success.800}", "type": "color"}, "borderRadius": {"value": "{global.borderRadius.full}", "type": "borderRadius"}, "paddingVertical": {"value": "{global.spacing.1}", "type": "spacing"}, "paddingHorizontal": {"value": "{global.spacing.2}", "type": "spacing"}, "fontSize": {"value": "{global.typography.fontSize.xs}", "type": "fontSizes"}, "fontWeight": {"value": "{global.typography.fontWeights.medium}", "type": "fontWeights"}}, "warning": {"backgroundColor": {"value": "{semantic.colors.status.warningLight}", "type": "color"}, "color": {"value": "{global.colors.warning.800}", "type": "color"}, "borderRadius": {"value": "{global.borderRadius.full}", "type": "borderRadius"}, "paddingVertical": {"value": "{global.spacing.1}", "type": "spacing"}, "paddingHorizontal": {"value": "{global.spacing.2}", "type": "spacing"}, "fontSize": {"value": "{global.typography.fontSize.xs}", "type": "fontSizes"}, "fontWeight": {"value": "{global.typography.fontWeights.medium}", "type": "fontWeights"}}, "error": {"backgroundColor": {"value": "{semantic.colors.status.errorLight}", "type": "color"}, "color": {"value": "{global.colors.error.800}", "type": "color"}, "borderRadius": {"value": "{global.borderRadius.full}", "type": "borderRadius"}, "paddingVertical": {"value": "{global.spacing.1}", "type": "spacing"}, "paddingHorizontal": {"value": "{global.spacing.2}", "type": "spacing"}, "fontSize": {"value": "{global.typography.fontSize.xs}", "type": "fontSizes"}, "fontWeight": {"value": "{global.typography.fontWeights.medium}", "type": "fontWeights"}}, "primary": {"backgroundColor": {"value": "{semantic.colors.brand.light}", "type": "color"}, "color": {"value": "{global.colors.primary.800}", "type": "color"}, "borderRadius": {"value": "{global.borderRadius.full}", "type": "borderRadius"}, "paddingVertical": {"value": "{global.spacing.1}", "type": "spacing"}, "paddingHorizontal": {"value": "{global.spacing.2}", "type": "spacing"}, "fontSize": {"value": "{global.typography.fontSize.xs}", "type": "fontSizes"}, "fontWeight": {"value": "{global.typography.fontWeights.medium}", "type": "fontWeights"}}}}}