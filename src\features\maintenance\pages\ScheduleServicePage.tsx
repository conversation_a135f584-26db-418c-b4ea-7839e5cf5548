import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  ArrowLeft, 
  Calendar, 
  Clock, 
  Wrench, 
  AlertTriangle, 
  CheckCircle,
  Car,
  User,
  MapPin,
  FileText,
  Save,
  X,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';

interface ServiceFormData {
  vehicleId: string;
  serviceType: string;
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
  scheduledDate: string;
  scheduledTime: string;
  estimatedDuration: string;
  preferredVendor: string;
  description: string;
  serviceItems: string[];
  currentMileage: string;
  notes: string;
}

const STEPS = [
  { id: 1, title: 'Vehicle', icon: Car },
  { id: 2, title: 'Service Details', icon: Wrench },
  { id: 3, title: 'Schedule', icon: Calendar },
  { id: 4, title: 'Items & Notes', icon: CheckCircle },
];

const ScheduleServicePage: React.FC = () => {
  const { vehicleId } = useParams<{ vehicleId?: string }>();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const [formData, setFormData] = useState<ServiceFormData>({
    vehicleId: vehicleId || '',
    serviceType: '',
    priority: 'Medium',
    scheduledDate: '',
    scheduledTime: '',
    estimatedDuration: '',
    preferredVendor: '',
    description: '',
    serviceItems: [],
    currentMileage: '',
    notes: ''
  });

  const vehicles = [
    { id: '1', registration: 'GP-123-ABC', make: 'Toyota', model: 'Corolla', department: 'Health' },
    { id: '2', registration: 'GP-456-DEF', make: 'Ford', model: 'Ranger', department: 'Public Works' },
    { id: '3', registration: 'GP-789-GHI', make: 'Nissan', model: 'NP200', department: 'Education' }
  ];

  const serviceTypes = [
    'Regular Service',
    'Oil Change',
    'Brake Service',
    'Tire Service',
    'Engine Repair',
    'Transmission Service',
    'Electrical Repair',
    'Body Work',
    'Air Conditioning',
    'Emergency Repair'
  ];

  const vendors = [
    'Fleet Services SA',
    'AutoFix Pro',
    'Quick Repair',
    'Premium Motors',
    'City Auto Service'
  ];

  const serviceItems = [
    'Engine Oil Change',
    'Oil Filter Replacement',
    'Air Filter Replacement',
    'Brake Pad Inspection',
    'Tire Rotation',
    'Battery Check',
    'Coolant Level Check',
    'Transmission Fluid Check',
    'Brake Fluid Check',
    'Power Steering Fluid Check'
  ];

  const progress = (currentStep / STEPS.length) * 100;
  const selectedVehicle = vehicles.find(v => v.id === formData.vehicleId);

  const updateFormData = (field: keyof ServiceFormData, value: string | string[]) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const toggleServiceItem = (item: string) => {
    const currentItems = formData.serviceItems;
    const updatedItems = currentItems.includes(item)
      ? currentItems.filter(i => i !== item)
      : [...currentItems, item];
    updateFormData('serviceItems', updatedItems);
  };

  const validateStep = (step: number) => {
    const newErrors: Record<string, string> = {};

    switch (step) {
      case 1:
        if (!formData.vehicleId) newErrors.vehicleId = 'Please select a vehicle';
        if (!formData.currentMileage) newErrors.currentMileage = 'Please enter current mileage';
        break;
      case 2:
        if (!formData.serviceType) newErrors.serviceType = 'Please select a service type';
        if (!formData.description.trim()) newErrors.description = 'Please provide a description';
        if (!formData.preferredVendor) newErrors.preferredVendor = 'Please select a vendor';
        break;
      case 3:
        if (!formData.scheduledDate) newErrors.scheduledDate = 'Please select a date';
        if (!formData.scheduledTime) newErrors.scheduledTime = 'Please select a time';
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, STEPS.length));
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateStep(currentStep)) return;

    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      navigate('/work-orders', {
        state: {
          message: `Service scheduled successfully for ${selectedVehicle?.registration}`
        }
      });
    } catch (error) {
      console.error('Error scheduling service:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="w-full space-y-6">
            <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Select Vehicle *
                </label>
                <select
                  value={formData.vehicleId}
                  onChange={(e) => updateFormData('vehicleId', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.vehicleId ? 'border-red-500' : 'border-gray-300'
                  }`}
                >
                  <option value="">Select a vehicle</option>
                  {vehicles.map(vehicle => (
                    <option key={vehicle.id} value={vehicle.id}>
                      {vehicle.registration} - {vehicle.make} {vehicle.model} ({vehicle.department})
                    </option>
                  ))}
                </select>
                {errors.vehicleId && (
                  <p className="mt-1 text-sm text-red-600">{errors.vehicleId}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Current Mileage *
                </label>
                <input
                  type="number"
                  value={formData.currentMileage}
                  onChange={(e) => updateFormData('currentMileage', e.target.value)}
                  placeholder="Enter current odometer reading"
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.currentMileage ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {errors.currentMileage && (
                  <p className="mt-1 text-sm text-red-600">{errors.currentMileage}</p>
                )}
              </div>
            </div>

            {selectedVehicle && (
              <div className="w-full p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">Selected Vehicle</h4>
                <p className="text-blue-800">
                  {selectedVehicle.registration} - {selectedVehicle.make} {selectedVehicle.model}
                </p>
                <p className="text-blue-600 text-sm">Department: {selectedVehicle.department}</p>
              </div>
            )}
          </div>
        );

      case 2:
        return (
          <div className="w-full space-y-6">
            <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Service Type *
                </label>
                <select
                  value={formData.serviceType}
                  onChange={(e) => updateFormData('serviceType', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.serviceType ? 'border-red-500' : 'border-gray-300'
                  }`}
                >
                  <option value="">Select service type</option>
                  {serviceTypes.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
                {errors.serviceType && (
                  <p className="mt-1 text-sm text-red-600">{errors.serviceType}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Priority
                </label>
                <select
                  value={formData.priority}
                  onChange={(e) => updateFormData('priority', e.target.value as ServiceFormData['priority'])}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="Low">Low</option>
                  <option value="Medium">Medium</option>
                  <option value="High">High</option>
                  <option value="Critical">Critical</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Preferred Vendor *
                </label>
                <select
                  value={formData.preferredVendor}
                  onChange={(e) => updateFormData('preferredVendor', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.preferredVendor ? 'border-red-500' : 'border-gray-300'
                  }`}
                >
                  <option value="">Select vendor</option>
                  {vendors.map(vendor => (
                    <option key={vendor} value={vendor}>{vendor}</option>
                  ))}
                </select>
                {errors.preferredVendor && (
                  <p className="mt-1 text-sm text-red-600">{errors.preferredVendor}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Estimated Duration
                </label>
                <select
                  value={formData.estimatedDuration}
                  onChange={(e) => updateFormData('estimatedDuration', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select duration</option>
                  <option value="1 hour">1 hour</option>
                  <option value="2 hours">2 hours</option>
                  <option value="Half day">Half day (4 hours)</option>
                  <option value="Full day">Full day (8 hours)</option>
                  <option value="2 days">2 days</option>
                  <option value="3-5 days">3-5 days</option>
                  <option value="1 week">1 week</option>
                  <option value="2+ weeks">2+ weeks</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Service Description *
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => updateFormData('description', e.target.value)}
                rows={3}
                placeholder="Describe the service needed, symptoms, or specific requirements..."
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.description ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {errors.description && (
                <p className="mt-1 text-sm text-red-600">{errors.description}</p>
              )}
            </div>
          </div>
        );

      case 3:
        return (
          <div className="w-full space-y-6">
            <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Preferred Date *
                </label>
                <input
                  type="date"
                  value={formData.scheduledDate}
                  onChange={(e) => updateFormData('scheduledDate', e.target.value)}
                  min={new Date().toISOString().split('T')[0]}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.scheduledDate ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {errors.scheduledDate && (
                  <p className="mt-1 text-sm text-red-600">{errors.scheduledDate}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Preferred Time *
                </label>
                <select
                  value={formData.scheduledTime}
                  onChange={(e) => updateFormData('scheduledTime', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.scheduledTime ? 'border-red-500' : 'border-gray-300'
                  }`}
                >
                  <option value="">Select time</option>
                  <option value="08:00">08:00 AM</option>
                  <option value="09:00">09:00 AM</option>
                  <option value="10:00">10:00 AM</option>
                  <option value="11:00">11:00 AM</option>
                  <option value="12:00">12:00 PM</option>
                  <option value="13:00">01:00 PM</option>
                  <option value="14:00">02:00 PM</option>
                  <option value="15:00">03:00 PM</option>
                  <option value="16:00">04:00 PM</option>
                </select>
                {errors.scheduledTime && (
                  <p className="mt-1 text-sm text-red-600">{errors.scheduledTime}</p>
                )}
              </div>
            </div>
          </div>
        );

      case 4:
        return (
          <div className="w-full space-y-6">
            <div>
              <h4 className="text-lg font-medium text-gray-900 mb-4">Service Items (Optional)</h4>
              <p className="text-sm text-gray-600 mb-4">Select specific items to be checked or serviced:</p>
              <div className="w-full grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {serviceItems.map(item => (
                  <label key={item} className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={formData.serviceItems.includes(item)}
                      onChange={() => toggleServiceItem(item)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">{item}</span>
                  </label>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Additional Notes
              </label>
              <textarea
                value={formData.notes}
                onChange={(e) => updateFormData('notes', e.target.value)}
                rows={3}
                placeholder="Any additional instructions, special requirements, or notes for the service provider..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="container mx-auto px-4 py-6">
      {/* Header */}
      <div className="mb-6">
        <Button
          variant="ghost"
          onClick={() => navigate(vehicleId ? `/vehicles/${vehicleId}` : '/vehicles')}
          className="mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Vehicles
        </Button>
        
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Schedule Service</h1>
          <p className="text-gray-600">Book maintenance or repair service for your vehicle</p>
        </div>
      </div>

      {/* Form Card */}
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Service Scheduling</CardTitle>
        </CardHeader>
        <CardContent className="w-full">
          <div className="w-full space-y-6">
            {/* Progress Bar */}
            <div className="w-full space-y-2">
              <div className="flex items-center justify-between w-full">
                <h2 className="text-lg font-semibold">Service Scheduling Progress</h2>
                <span className="text-sm text-gray-600">
                  Step {currentStep} of {STEPS.length}
                </span>
              </div>
              <Progress value={progress} className="h-2 w-full" />
            </div>

            {/* Step Navigation */}
            <div className="w-full grid grid-cols-2 md:grid-cols-4 gap-2">
              {STEPS.map((step) => {
                const Icon = step.icon;
                return (
                  <div
                    key={step.id}
                    className={cn(
                      "flex items-center space-x-2 px-3 py-2 rounded-lg text-sm",
                      currentStep === step.id
                        ? "bg-blue-100 text-blue-700"
                        : currentStep > step.id
                        ? "bg-green-100 text-green-700"
                        : "bg-gray-100 text-gray-500"
                    )}
                  >
                    <Icon className="h-4 w-4" />
                    <span className="hidden sm:block text-xs lg:text-sm">{step.title}</span>
                  </div>
                );
              })}
            </div>

            {/* Form Content */}
            <form onSubmit={handleSubmit} className="w-full space-y-6">
              <Card className="w-full">
                <CardHeader className="w-full">
                  <CardTitle className="flex items-center gap-2">
                    {React.createElement(STEPS[currentStep - 1].icon, { className: "h-5 w-5" })}
                    {STEPS[currentStep - 1].title}
                  </CardTitle>
                </CardHeader>
                <CardContent className="w-full p-6">
                  <div className="w-full">
                    {renderStepContent()}
                  </div>
                </CardContent>
              </Card>

              {/* Navigation Buttons */}
              <div className="flex items-center justify-between w-full">
                <Button
                  type="button"
                  variant="outline"
                  onClick={prevStep}
                  disabled={currentStep === 1}
                >
                  <ChevronLeft className="h-4 w-4 mr-2" />
                  Previous
                </Button>

                <div className="flex items-center gap-3">
                  <Button 
                    type="button" 
                    variant="ghost" 
                    onClick={() => navigate(vehicleId ? `/vehicles/${vehicleId}` : '/vehicles')}
                  >
                    Cancel
                  </Button>

                  {currentStep < STEPS.length ? (
                    <Button type="button" onClick={nextStep}>
                      Next
                      <ChevronRight className="h-4 w-4 ml-2" />
                    </Button>
                  ) : (
                    <Button type="submit" disabled={isLoading}>
                      <Save className="h-4 w-4 mr-2" />
                      {isLoading ? 'Scheduling...' : 'Schedule Service'}
                    </Button>
                  )}
                </div>
              </div>
            </form>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ScheduleServicePage;

