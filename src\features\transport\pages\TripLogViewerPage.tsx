import React, { useState } from 'react';
import { 
  Car, 
  User, 
  MapPin, 
  Clock, 
  Fuel, 
  Route,
  Calendar,
  Filter,
  Download,
  Search,
  TrendingUp
} from 'lucide-react';

interface TripLog {
  id: string;
  vehicleId: string;
  registrationNumber: string;
  driverId: string;
  driverName: string;
  startLocation: string;
  endLocation: string;
  startTime: string;
  endTime: string;
  distance: number;
  fuelConsumed: number;
  purpose: string;
  department: string;
  status: 'completed' | 'in-progress' | 'cancelled';
}

const TripLogViewerPage: React.FC = () => {
  const [filterBy, setFilterBy] = useState<'vehicle' | 'driver'>('vehicle');
  const [selectedVehicle, setSelectedVehicle] = useState('');
  const [selectedDriver, setSelectedDriver] = useState('');
  const [dateRange, setDateRange] = useState({ start: '', end: '' });
  const [searchTerm, setSearchTerm] = useState('');

  const tripLogs: TripLog[] = [
    {
      id: '1',
      vehicleId: '1',
      registrationNumber: 'GP-123-ABC',
      driverId: '1',
      driverName: '<PERSON>',
      startLocation: 'Main Depot',
      endLocation: 'City Hall',
      startTime: '2024-01-15T08:00:00',
      endTime: '2024-01-15T09:30:00',
      distance: 25.5,
      fuelConsumed: 3.2,
      purpose: 'Official Meeting',
      department: 'Public Works',
      status: 'completed'
    },
    {
      id: '2',
      vehicleId: '2',
      registrationNumber: 'GP-456-DEF',
      driverId: '2',
      driverName: 'Jane Smith',
      startLocation: 'City Hall',
      endLocation: 'Hospital',
      startTime: '2024-01-15T10:00:00',
      endTime: '2024-01-15T11:15:00',
      distance: 18.3,
      fuelConsumed: 2.1,
      purpose: 'Medical Supplies Delivery',
      department: 'Health',
      status: 'completed'
    },
    {
      id: '3',
      vehicleId: '1',
      registrationNumber: 'GP-123-ABC',
      driverId: '3',
      driverName: 'Mike Johnson',
      startLocation: 'Main Depot',
      endLocation: 'School District A',
      startTime: '2024-01-16T07:30:00',
      endTime: '',
      distance: 0,
      fuelConsumed: 0,
      purpose: 'Equipment Transport',
      department: 'Education',
      status: 'in-progress'
    }
  ];

  const vehicles = [
    { id: '1', registrationNumber: 'GP-123-ABC' },
    { id: '2', registrationNumber: 'GP-456-DEF' },
    { id: '3', registrationNumber: 'GP-789-GHI' }
  ];

  const drivers = [
    { id: '1', name: 'John Doe' },
    { id: '2', name: 'Jane Smith' },
    { id: '3', name: 'Mike Johnson' }
  ];

  const filteredLogs = tripLogs.filter(log => {
    const matchesVehicle = !selectedVehicle || log.vehicleId === selectedVehicle;
    const matchesDriver = !selectedDriver || log.driverId === selectedDriver;
    const matchesSearch = !searchTerm || 
      log.registrationNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.driverName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.purpose.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.department.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesVehicle && matchesDriver && matchesSearch;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'in-progress': return 'bg-blue-100 text-blue-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDuration = (startTime: string, endTime: string) => {
    if (!endTime) return 'In Progress';
    const start = new Date(startTime);
    const end = new Date(endTime);
    const diffMs = end.getTime() - start.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    return `${diffHours}h ${diffMinutes}m`;
  };

  const formatTime = (dateTime: string) => {
    return new Date(dateTime).toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const formatDate = (dateTime: string) => {
    return new Date(dateTime).toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric' 
    });
  };

  const calculateTotalStats = () => {
    const completedTrips = filteredLogs.filter(log => log.status === 'completed');
    const totalDistance = completedTrips.reduce((sum, log) => sum + log.distance, 0);
    const totalFuel = completedTrips.reduce((sum, log) => sum + log.fuelConsumed, 0);
    const avgFuelEfficiency = totalDistance > 0 ? totalDistance / totalFuel : 0;

    return {
      totalTrips: completedTrips.length,
      totalDistance: totalDistance.toFixed(1),
      totalFuel: totalFuel.toFixed(1),
      avgEfficiency: avgFuelEfficiency.toFixed(1)
    };
  };

  const stats = calculateTotalStats();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Trip Log Viewer</h1>
          <p className="text-gray-600">Review trip histories, distances, and fuel consumption</p>
        </div>
        <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2">
          <Download className="h-4 w-4" />
          <span>Export Data</span>
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Trips</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalTrips}</p>
            </div>
            <Route className="h-6 w-6 text-blue-600" />
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Distance</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalDistance} km</p>
            </div>
            <MapPin className="h-6 w-6 text-green-600" />
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Fuel</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalFuel} L</p>
            </div>
            <Fuel className="h-6 w-6 text-orange-600" />
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Efficiency</p>
              <p className="text-2xl font-bold text-gray-900">{stats.avgEfficiency} km/L</p>
            </div>
            <TrendingUp className="h-6 w-6 text-purple-600" />
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search trips..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          
          <div className="flex gap-4">
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setFilterBy('vehicle')}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  filterBy === 'vehicle' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600'
                }`}
              >
                By Vehicle
              </button>
              <button
                onClick={() => setFilterBy('driver')}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  filterBy === 'driver' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600'
                }`}
              >
                By Driver
              </button>
            </div>

            {filterBy === 'vehicle' ? (
              <select
                value={selectedVehicle}
                onChange={(e) => setSelectedVehicle(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Vehicles</option>
                {vehicles.map(vehicle => (
                  <option key={vehicle.id} value={vehicle.id}>{vehicle.registrationNumber}</option>
                ))}
              </select>
            ) : (
              <select
                value={selectedDriver}
                onChange={(e) => setSelectedDriver(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Drivers</option>
                {drivers.map(driver => (
                  <option key={driver.id} value={driver.id}>{driver.name}</option>
                ))}
              </select>
            )}

            <input
              type="date"
              value={dateRange.start}
              onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            />
            <input
              type="date"
              value={dateRange.end}
              onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>

      {/* Trip Logs Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Trip History</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Vehicle & Driver
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Route
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date & Time
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Distance & Fuel
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Purpose
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredLogs.map((log) => (
                <tr key={log.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-3">
                      <Car className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">{log.registrationNumber}</p>
                        <p className="text-sm text-gray-500 flex items-center">
                          <User className="h-3 w-3 mr-1" />
                          {log.driverName}
                        </p>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm">
                      <p className="text-gray-900 flex items-center">
                        <MapPin className="h-3 w-3 mr-1 text-green-500" />
                        {log.startLocation}
                      </p>
                      <p className="text-gray-500 flex items-center mt-1">
                        <MapPin className="h-3 w-3 mr-1 text-red-500" />
                        {log.endLocation}
                      </p>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm">
                      <p className="text-gray-900">{formatDate(log.startTime)}</p>
                      <p className="text-gray-500 flex items-center">
                        <Clock className="h-3 w-3 mr-1" />
                        {formatTime(log.startTime)} - {log.endTime ? formatTime(log.endTime) : 'Ongoing'}
                      </p>
                      <p className="text-xs text-gray-400">
                        Duration: {formatDuration(log.startTime, log.endTime)}
                      </p>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm">
                      <p className="text-gray-900">{log.distance} km</p>
                      <p className="text-gray-500 flex items-center">
                        <Fuel className="h-3 w-3 mr-1" />
                        {log.fuelConsumed} L
                      </p>
                      {log.distance > 0 && log.fuelConsumed > 0 && (
                        <p className="text-xs text-gray-400">
                          {(log.distance / log.fuelConsumed).toFixed(1)} km/L
                        </p>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm">
                      <p className="text-gray-900">{log.purpose}</p>
                      <p className="text-gray-500">{log.department}</p>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(log.status)}`}>
                      {log.status.replace('-', ' ').toUpperCase()}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default TripLogViewerPage;