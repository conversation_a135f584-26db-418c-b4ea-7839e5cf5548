# RT46-2026 Fleet Management System - Functional Features by User Role

## Overview

This document outlines the user-specific functional features for the RT46-2026 Vehicle Fleet Management System, based on the Product Requirements Document (PRD). The features are organized by primary and secondary user types, ensuring each user role has appropriate access levels and relevant functionality.

---

## Primary Users

### 1. Fleet Managers

#### Dashboard Features
- Fleet overview with departmental vehicle counts
- Maintenance cost analytics and budget tracking
- Vehicle downtime reports and alerts
- Vendor performance scorecards
- Budget vs. actual spending widgets

#### Core Modules Access
- Full vehicle management (view, create, update, assign)
- Work order creation and approval workflows
- Vendor performance monitoring
- Advanced reporting and analytics
- Budget and cost management

### 2. Transport Officers

#### Dashboard Features
- Daily operations overview
- Vehicle availability status
- Pending work orders requiring attention
- Driver assignments and schedules

#### Core Modules Access
- Vehicle management (view, update status)
- Work order management (create, track)
- Basic vendor interaction
- Trip and fuel management
- Maintenance scheduling

### 3. Drivers

#### Mobile App Features
- Vehicle booking and trip authorization
- Pre-trip inspection checklists
- Issue reporting (breakdowns, accidents)
- Fuel consumption logging
- Trip start/end recording

#### Limited Web Access
- Personal trip history
- Vehicle assignment status
- Basic issue reporting

### 4. Finance Officers

#### Dashboard Features
- Payment processing queue
- Budget tracking and alerts
- Cost analysis by department/vehicle
- Vendor payment status

#### Core Modules Access
- Invoice processing and approval
- Payment tracking and reconciliation
- Budget management
- Financial reporting
- Vendor payment history

---

## Secondary Users

### 1. Vendors/Merchants

#### Vendor Portal Features
- Work order notifications and queue
- Quote submission interface
- Job progress tracking
- Invoice submission
- Payment status tracking
- Performance metrics dashboard

#### Limited Access
- Only work orders assigned to them
- Communication with fleet managers
- Document upload for compliance

### 2. Inspectors

#### Mobile App Features
- Inspection checklists and forms
- Photo/video capture for compliance
- GPS location tagging
- Signature capture
- Offline inspection capability

#### Web Portal Access
- Inspection scheduling
- Compliance reporting
- Quality assurance dashboards

### 3. Executives (Department Heads)

#### Executive Dashboard
- High-level KPIs and metrics
- Cost savings and efficiency reports
- Fleet utilization summaries
- Strategic decision support data

#### Read-Only Access
- Summary reports and analytics
- Budget performance
- Compliance status overview

### 4. Auditors

#### Audit Interface
- Complete audit trail access
- Compliance reporting tools
- Data export capabilities
- Historical data analysis

#### Specialized Features
- Immutable audit logs
- Compliance checklist tools
- Report generation
- Data integrity verification

---

## Role-Based UI Customization

### Navigation Menus by Role

| Role | Navigation Menu Items |
|------|----------------------|
| **Fleet Manager** | Dashboard, Vehicles, Work Orders, Vendors, Reports, Users |
| **Transport Officer** | Dashboard, Vehicles, Work Orders, Maintenance, Trips |
| **Driver** | Mobile App, Basic Web (Trips, Issues) |
| **Finance Officer** | Dashboard, Invoices, Payments, Budgets, Reports |
| **Vendor** | Work Orders, Quotes, Invoices, Payments, Profile |
| **Inspector** | Inspections, Compliance, Reports |
| **Executive** | Dashboard, Reports, Analytics |
| **Auditor** | Audit Logs, Compliance, Reports, Data Export |

### Permission-Based Feature Access

- **Create/Edit Permissions:** Only authorized roles can modify data
- **Approval Workflows:** Different approval levels based on role hierarchy
- **Data Visibility:** Department-based and role-based data filtering
- **Action Buttons:** Contextual actions based on user permissions

This role-based approach ensures each user type sees only relevant features and has appropriate access levels for their responsibilities.

---

# Fleet Management System (RT46-2026): UI/UX Design Specification

## Introduction

This document outlines the user stories, screen wireframes, and design rationale for the core workflows of the National Treasury's Fleet Management System. The design is based on the requirements detailed in the RT46-2026 tender document. The approach is systematic, building feature upon feature in logical phases that follow the primary user journeys through the system.

---

## Phase 1: Fleet Manager Dashboard & Triage

This phase establishes the main entry point for a Fleet Manager, providing a high-level overview of the fleet and enabling them to quickly identify and act on urgent issues.

**User Persona:** Fleet Manager  
**Core Epic:** Fleet Oversight and Initial Maintenance Triage

### User Stories

1. **Dashboard Overview:** As a Fleet Manager, I want to see a high-level dashboard with key fleet metrics (like vehicle status and costs) when I first log in, so that I can quickly assess the overall health and operational status of my fleet without needing to run multiple reports.

2. **Immediate Action Triage:** As a Fleet Manager, I want a clear, prioritized list of items that require my immediate action, such as "Maintenance Approvals Pending", so that I can prevent bottlenecks and keep vehicle repairs and maintenance moving forward efficiently, minimizing downtime.

3. **Navigating to Problems:** As a Fleet Manager, I want to be able to click directly from a dashboard alert to the relevant screen that allows me to take action, so that I can resolve issues with the minimum number of clicks.

### Screens & Design Rationale

#### Screen 1: Fleet Manager Dashboard

```
+---------------------------------------------------------------------------------------------------+
| [Company Logo]  Fleet Management System | RT46-2026          [Fleet Manager Name ▼] [🔔 3] [Logout] |
|---------------------------------------------------------------------------------------------------|
|  Dashboard   |  Vehicles  |  Maintenance  |  Merchants  |  Tracking  |  Reports  |  Admin          |
+---------------------------------------------------------------------------------------------------+
|                                                                                                   |
|  DASHBOARD > OVERVIEW                                                                             |
|  -------------------------------------------------------------------------------------------------  |
|                                                                                                   |
|  [ ACTION REQUIRED (3) ]-----------------------   [ FLEET STATUS ]-----------------------------  |
|  |                                           |   |                                           |  |
|  | -> 2 Maintenance Approvals Pending        |   |    [---------------------]                |  |
|  | -> 1 Service Overdue                      |   |    |    / \                |   250 Active |  |
|  |                                           |   |    |   /   \               |    50 In Shop|  |
|  |                                           |   |    |  /-----\              |    10 VOR*   |  |
|  |                                           |   |    [---------------------]                |  |
|  ---------------------------------------------   ---------------------------------------------  |
|                                                                                                   |
|  [ MONTH-TO-DATE SPEND ]-----------------------   [ RECENT ACTIVITY ]--------------------------  |
|  |                                           |   |                                           |  |
|  |   Maintenance: R 150,450.00               |   | - Vehicle AB123CD assigned to J. Smith    |  |
|  |   Fuel:         R 320,100.00               |   | - Maint. Request #789 approved            |  |
|  |   Toll:          R  15,230.00               |   | - New merchant "ABC Motors" added         |  |
|  |   ---------------------------               |   |                                           |  |
|  |   Total:       R 485,780.00               |   |                                           |  |
|  ---------------------------------------------   ---------------------------------------------  |
|                                                                    *Vehicle Off Road              |
+---------------------------------------------------------------------------------------------------+
```

#### Screen 2: Maintenance Requests List

```
+---------------------------------------------------------------------------------------------------+
| [Company Logo]  Fleet Management System | RT46-2026          [Fleet Manager Name ▼] [🔔 3] [Logout] |
|---------------------------------------------------------------------------------------------------|
|  Dashboard   |  Vehicles  | >Maintenance< |  Merchants  |  Tracking  |  Reports  |  Admin          |
+---------------------------------------------------------------------------------------------------+
|                                                                                                   |
|  MAINTENANCE > ALL REQUESTS                                      [+ Log New Request] Button       |
|  -------------------------------------------------------------------------------------------------  |
|                                                                                                   |
|  Filters: [Status: Pending Approval ▼] [Priority: All ▼] [Date Range] [ Search by Reg/VIN...  🔍 ] |
|                                                                                                   |
|  -------------------------------------------------------------------------------------------------  |
|  | ID   | VEHICLE       | ISSUE SUMMARY                  | STATUS              | DATE   | ACTION |  |
|  |------|---------------|--------------------------------|---------------------|--------|--------|  |
|  | #791 | GP 456-XYZ    | Engine running hot, steam      | [Pending Approval]  | 17/07  | [View] |  |
|  |      | Ford Ranger   | from hood. Driver reported.    | (High Priority)     |        |        |  |
|  |------|---------------|--------------------------------|---------------------|--------|--------|  |
|  | #790 | EC 987-CBA    | 60,000 km Major Service        | [Pending Approval]  | 17/07  | [View] |  |
|  |      | Toyota Hilux  |                                | (Medium Priority)   |        |        |  |
|  -------------------------------------------------------------------------------------------------  |
+---------------------------------------------------------------------------------------------------+
```

**Design Rationale:** The dashboard provides an "at-a-glance" overview with a prominent "Action Required" card to guide the user's attention. This card contains direct links to pre-filtered lists, such as the Maintenance Requests List, allowing the manager to triage and act on pressing issues with maximum efficiency.

---

## Phase 2: Maintenance Request Approval Workflow

This phase details the critical workflow where a Fleet Manager reviews a specific maintenance request and makes a data-driven decision on the quotation.

**User Persona:** Fleet Manager  
**Core Epic:** Maintenance Request Approval

### User Stories

1. **Viewing Consolidated Information:** As a Fleet Manager, I want to view all details related to a single maintenance request on one screen, including vehicle info, the reported issue, and the merchant's quotation, so that I can have the complete context before making a decision.

2. **Validating Quotation Fairness:** As a Fleet Manager, I want to see a line-by-line comparison of the merchant's quoted price against the system's benchmark "fair price" for both parts and labor, so that I can easily identify and question any potential over-charging.

3. **Making an Informed Decision:** As a Fleet Manager, I want to have clear action buttons to "Approve" or "Reject" the quotation, and a space to add mandatory comments for a rejection, so that I can process the request efficiently and maintain a clear audit trail of my decisions.

### Screen & Design Rationale

#### Screen 3: Maintenance Request Detail & Approval

```
+---------------------------------------------------------------------------------------------------+
| [Company Logo]  Fleet Management System | RT46-2026          [Fleet Manager Name ▼] [🔔 2] [Logout] |
|---------------------------------------------------------------------------------------------------|
|  Dashboard   |  Vehicles  | >Maintenance< |  Merchants  |  Tracking  |  Reports  |  Admin          |
+---------------------------------------------------------------------------------------------------+
|                                                                                                   |
|  < Back to All Requests | MAINTENANCE > REQUEST #791                                               |
|  -------------------------------------------------------------------------------------------------  |
|                                                                                                   |
|  [ REQUEST SUMMARY ]----------------------------------------------------------------------------  |
|  | Vehicle: GP 456-XYZ (Ford Ranger) | Status: [Pending Approval] (High)                         |  |
|  | Odometer: 125,678 km              | Merchant: ABC Motors (Approved)                           |  |
|  -----------------------------------------------------------------------------------------------  |
|                                                                                                   |
|  [ QUOTATION VALIDATION ]-----------------------------------------------------------------------  |
|  |                                                                                             |  |
|  | PART/LABOUR                      | QTY | MERCHANT QUOTE | SYSTEM BENCHMARK | VARIANCE      |  |
|  |----------------------------------|-----|----------------|------------------|---------------|  |
|  | Water Pump (OEM Part)            |  1  |   R 2,450.00   |   R 2,200.00     | [ R 250.00 ▲ ]|  |
|  | Labour: Replace water pump       | 2.5 |   R 1,875.00   |   R 1,500.00     | [ R 375.00 ▲ ]|  |
|  |----------------------------------|-----|----------------|------------------|---------------|  |
|  | TOTAL                            |     |   R 5,495.00   |   R 4,850.00     | [ R 645.00 ▲ ]|  |
|  -----------------------------------------------------------------------------------------------  |
|                                                                                                   |
|  [ DECISION & COMMENTS ]------------------------------------------------------------------------  |
|  |                                                                                             |  |
|  |  Comments (required for rejection): [_____________________________________________________]  |  |
|  |                                                                                             |  |
|  |                                      [ REJECT QUOTE ] Button    [ APPROVE QUOTE ] Button     |  |
|  -----------------------------------------------------------------------------------------------  |
+---------------------------------------------------------------------------------------------------+
```

**Design Rationale:** This screen empowers the Fleet Manager by presenting all decision-making information in one place. The "Quotation Validation" section is the core feature, using visual cues to highlight discrepancies between the merchant's quote and the system's fair-price benchmark. This transforms the manager's role from a rubber-stamper to a data-driven financial controller.

---

## Phase 3: Merchant Workflow (Order to Invoice)

This phase covers the entire lifecycle of a job from the Merchant's perspective, from receiving the approved work order to submitting the final invoice for payment.

**User Persona:** Merchant (Workshop/Service Provider)  
**Core Epic:** Fulfilling a Service Order and Invoicing

### User Stories

1. **Notification and Details:** As a Merchant, I want to receive a notification and a clear, non-editable work order on my portal as soon as a job is approved, so that I know exactly what work is authorized and can schedule it without delay.

2. **Status Updates:** As a Merchant, I want to be able to easily update the status of the job in the portal (e.g., "In Progress," "Work Complete"), so that the Fleet Manager is kept informed and I can trigger the final invoicing step.

3. **Error-Proof Invoicing:** As a Merchant, I want to generate an invoice directly from the completed work order with a single click, so that I don't have to re-enter line items, which saves time and prevents billing errors.

4. **Payment Tracking:** As a Merchant, I want to see a list of all submitted invoices and track their payment status, so that I have clarity on my accounts receivable and know when to expect payment.

### Screens & Design Rationale

#### Screen 4: Merchant Dashboard

```
+---------------------------------------------------------------------------------------------------+
| [Company Logo]  Merchant Portal | RT46-2026                 [Merchant Name: ABC Motors ▼] [Logout] |
|---------------------------------------------------------------------------------------------------|
|  Dashboard   |  Work Orders  |  Invoices  |  Profile                                              |
+---------------------------------------------------------------------------------------------------+
|                                                                                                   |
|  [ NEW WORK ORDERS (1) ]-----------------------   [ ACTIVE JOBS ]--------------------------------  |
|  |                                           |   |                                           |  |
|  | -> WO-001234: Ford Ranger (GP 456-XYZ)   |   |   2 In Progress                             |  |
|  |    [Accept & Schedule Job] Button        |   |   5 Scheduled                               |  |
|  |                                           |   |                                           |  |
|  ---------------------------------------------   ---------------------------------------------  |
+---------------------------------------------------------------------------------------------------+
```

#### Screen 5: Work Order Detail

```
+---------------------------------------------------------------------------------------------------+
| [Company Logo]  Merchant Portal | RT46-2026                 [Merchant Name: ABC Motors ▼] [Logout] |
|---------------------------------------------------------------------------------------------------|
|  Dashboard   | >Work Orders< |  Invoices  |  Profile                                              |
+---------------------------------------------------------------------------------------------------+
|  < Back to All Work Orders | WORK ORDER #WO-001234                                                  |
|  -------------------------------------------------------------------------------------------------  |
|  [ APPROVED WORK & PARTS (Non-Editable) ]-------------------------------------------------------  |
|  | PART/LABOUR                      | QTY | APPROVED PRICE                                      |  |
|  |----------------------------------|-----|-----------------------------------------------------|  |
|  | Water Pump (OEM Part)            |  1  |   R 2,450.00                                        |  |
|  | Labour: Replace water pump       | 2.5 |   R 1,875.00                                        |  |
|  -----------------------------------------------------------------------------------------------  |
|                                                                                                   |
|  [ JOB STATUS MANAGEMENT ]----------------------------------------------------------------------  |
|  |  Current Status: [In Progress]                                                              |  |
|  |  Update Status To: [ Work Complete ▼] [Update Status & Generate Invoice] Button             |  |
|  -----------------------------------------------------------------------------------------------  |
+---------------------------------------------------------------------------------------------------+
```

#### Screen 6: Invoice Generation

```
+---------------------------------------------------------------------------------------------------+
| [Company Logo]  Merchant Portal | RT46-2026                 [Merchant Name: ABC Motors ▼] [Logout] |
|---------------------------------------------------------------------------------------------------|
|  Dashboard   |  Work Orders  | >Invoices< |  Profile                                              |
+---------------------------------------------------------------------------------------------------+
|  < Back to Work Order | INVOICE > #INV-0099 (DRAFT)                                                |
|  -------------------------------------------------------------------------------------------------  |
|  From: ABC Motors                                          To: Gauteng Dept. of Health            |
|  Invoice Date: 19 July 2025                                Due Date: 18 August 2025 (Net 30)      |
|  Reference:   Work Order #WO-001234                                                               |
|  -------------------------------------------------------------------------------------------------  |
|  [ INVOICE LINE ITEMS (from approved work order - non-editable) ]                                 |
|  | PART/LABOUR                      | QTY | APPROVED PRICE                                      |  |
|  |----------------------------------|-----|-----------------------------------------------------|  |
|  | Water Pump (OEM Part)            |  1  |   R 2,450.00                                        |  |
|  -----------------------------------------------------------------------------------------------  |
|                                         [ Save as Draft ] [ Cancel ] [ SUBMIT INVOICE ] Button    |
+---------------------------------------------------------------------------------------------------+
```

#### Screen 7: Invoice Tracking

```
+---------------------------------------------------------------------------------------------------+
| [Company Logo]  Merchant Portal | RT46-2026                 [Merchant Name: ABC Motors ▼] [Logout] |
|---------------------------------------------------------------------------------------------------|
|  Dashboard   |  Work Orders  | >Invoices< |  Profile                                              |
+---------------------------------------------------------------------------------------------------+
|  INVOICES > ALL INVOICES                                                                          |
|  -------------------------------------------------------------------------------------------------  |
|  Filters: [Status: All ▼] [Date Range] [ Search by Invoice #... 🔍 ]                               |
|  -------------------------------------------------------------------------------------------------  |
|  | INVOICE #  | SUBMITTED | DUE DATE  | AMOUNT         | STATUS                   |                 |
|  |------------|-----------|-----------|----------------|--------------------------|                 |
|  | #INV-0099  | 19/07/25  | 18/08/25  | R 5,495.00     | [ Submitted       ]      |                 |
|  | #INV-0098  | 15/07/25  | 14/08/25  | R 12,500.00    | [ Approved for Payment ] |                 |
|  | #INV-0096  | 01/07/25  | 31/07/25  | R 8,950.00     | [ Paid (16/07/25) ]      |                 |
|  -------------------------------------------------------------------------------------------------  |
+---------------------------------------------------------------------------------------------------+
```

**Design Rationale:** The Merchant Portal is a simplified, role-specific interface. The workflow is designed for efficiency and transparency. A key feature is the auto-generation of a non-editable invoice from the approved work order, which eliminates billing errors and ensures data integrity. The final invoice tracking screen provides merchants with self-service capability to monitor payment status, improving supplier relationships and administrative efficiency.

---

## Phase 4: Asset Management (Onboarding a New Vehicle)

This phase focuses on the foundational process of adding a new vehicle to the fleet, ensuring data accuracy from the very beginning.

**User Persona:** Fleet Administrator  
**Core Epic:** Asset Management - Onboarding New Vehicles

### User Stories

1. **Guided Data Entry:** As a Fleet Administrator, I want to be guided through a step-by-step process when adding a new vehicle, with fields grouped into logical sections, so that the process feels manageable and I can ensure all required information is captured correctly.

2. **Bulk Upload:** As a Fleet Administrator, I want to be able to upload a spreadsheet (XLSX) to add multiple vehicles at once, so that I can efficiently onboard a large batch of new vehicles.

3. **Data Validation:** As a Fleet Administrator, I want the system to validate the data I enter before the record is saved, so that I can catch and correct errors immediately, ensuring the integrity of the asset data.

### Screens & Design Rationale

#### Screen 8: Vehicle Identity Step

```
+---------------------------------------------------------------------------------------------------+
|  < Back to Vehicle List | ADD NEW VEHICLE                                                          |
|  -------------------------------------------------------------------------------------------------  |
|  [ Step 1: Vehicle Identity ] -> [ Step 2: Assignment ] -> [ Step 3: Service Plan ]               |
|  -------------------------------------------------------------------------------------------------  |
|  Vehicle Identification Number (VIN)*  [_______________________________________] (Validate format)  |
|  Make* [ Toyota ▼ ]                   Model* [ Hilux 2.8 GD-6 ▼ ]                                  |
|                                                                     [ Cancel ] [ Next: Assignment > ] |
+---------------------------------------------------------------------------------------------------+
```

#### Screen 9: Assignment & Ownership Step

```
+---------------------------------------------------------------------------------------------------+
|  < Back to Vehicle List | ADD NEW VEHICLE                                                          |
|  -------------------------------------------------------------------------------------------------  |
|  [ Step 1 ] -> [ Step 2: Assignment & Ownership ] -> [ Step 3: Service Plan ]                     |
|  -------------------------------------------------------------------------------------------------  |
|  Owning Department* [ Gauteng > Department of Health ▼ ]                                          |
|  Current Odometer*  [ 150         ] km                                                          |
|                                              [ < Back: Identity ] [ Cancel ] [ Next: Service Plan > ] |
+---------------------------------------------------------------------------------------------------+
```

#### Screen 10: Service Plan & Warranty Step

```
+---------------------------------------------------------------------------------------------------+
|  < Back to Vehicle List | ADD NEW VEHICLE                                                          |
|  -------------------------------------------------------------------------------------------------  |
|  [ Step 1 ] -> [ Step 2 ] -> [ Step 3: Service Plan & Warranty ]                                  |
|  -------------------------------------------------------------------------------------------------  |
|  Does this vehicle have an active Service or Maintenance Plan?*  ( ) No (●) Yes                   |
|  Plan Expiry* [ 31/07/2028 ] OR [ 90,000 ] km (whichever comes first)                              |
|  Service every* [ 15,000 ] km OR [ 12 ] months                                                    |
|                                       [ < Back: Assignment ] [ Cancel ] [ SAVE VEHICLE ] Button   |
+---------------------------------------------------------------------------------------------------+
```

**Design Rationale:** A multi-step "wizard" is used to break down a complex data entry task into manageable, logical sections. This guided approach reduces cognitive load on the user. The use of dropdowns, data validation, and clear required fields minimizes errors and ensures the creation of a clean, reliable asset register, which is the foundation for all other system functions.

---

## Phase 5: Electronic Accident Repair

This workflow is distinct from standard maintenance. It often involves more complex assessments, different types of merchants (panel beaters), and insurance involvement.

**User Persona:** Fleet Manager / Accident Assessor  
**Core Epic:** Managing an Accident Repair Job

### User Stories

1. **Digital Damage Assessment:** As an Assessor, I want to use a mobile app or portal to upload photos of vehicle damage, annotating specific areas and linking them to required repair operations (e.g., "Replace fender," "Repair door panel"), so that I can create a detailed and accurate digital assessment report.

2. **Automated Parts & Labor Quoting:** As a System, based on the assessor's input, I want to automatically generate a preliminary quotation by pulling prices for required parts from an industry-standard database (e.g., Audatex) and applying standardized labor times for each operation, so that the initial quote is based on objective data.

3. **Merchant Quotation and Comparison:** As a Fleet Manager, I want to send the system-generated quote to an approved panel beater and receive their quote in return, and see a side-by-side comparison similar to the maintenance workflow, so I can validate their pricing against the industry benchmark.

### Screen & Design Rationale

This screen would be a specialized version of the Maintenance Request screen.

#### Screen 11: Accident Repair Case Management

```
+---------------------------------------------------------------------------------------------------+
| < Back to Cases | ACCIDENT REPAIR CASE #AC-2025-101                                                  |
| -------------------------------------------------------------------------------------------------  |
|                                                                                                   |
| [ DAMAGE ASSESSMENT (Input from Assessor's App) ]----------------------------------------------  |
| |                                                                                             |  |
| |  [ Interactive Vehicle Diagram ]         Damage Points:                                     |  |
| |  [  Front-Left Fender (Clicked) ]         1. Front-Left Fender: Replace                      |  |
| |  [        _________           ]         2. Front Bumper: Repair & Respray                  |  |
| |  [       /  _|_|_  \          ]         3. Headlight Assy (Left): Replace                    |  |
| |  [      | |  O O  | |         ]                                                              |  |
| |  [      \_________/          ]                                                              |  |
| |                                                                                             |  |
| -----------------------------------------------------------------------------------------------  |
|                                                                                                   |
| [ AUTOMATED QUOTATION (vs. Merchant Quote) ]---------------------------------------------------  |
| |                                                                                             |  |
| | OPERATION                  | SYSTEM BENCHMARK (Audatex) | MERCHANT QUOTE | VARIANCE      |  |
| |----------------------------|----------------------------|----------------|---------------|  |
| | Replace Fender (Parts)     |        R 4,500.00          |   R 4,800.00   | [ R 300.00 ▲ ]|  |
| | Replace Fender (Labour: 4hr) |        R 3,400.00          |   R 3,400.00   |     R 0.00    |  |
| | Repair Bumper (Labour: 3hr)|        R 2,550.00          |   R 2,900.00   | [ R 350.00 ▲ ]|  |
| |----------------------------|----------------------------|----------------|---------------|  |
| | TOTAL                      |       R 15,650.00          |  R 16,300.00   | [ R 650.00 ▲ ]|  |
| -----------------------------------------------------------------------------------------------  |
|                                                     [ Approve/Reject Quote ] Button             |
+---------------------------------------------------------------------------------------------------+
```

**Design Rationale:** The key is the two-stage quoting process. The system first generates an objective, data-driven benchmark quote based on the digital assessment. This benchmark is then used to validate the quote received from the human panel beater. The interactive diagram makes the damage assessment clear and intuitive.

---

## Phase 6: Live Fleet Monitoring & Security

This phase deals with the real-time tracking and monitoring capabilities of the system. The focus is on providing immediate operational awareness, enhancing driver safety, and ensuring vehicles are being used appropriately. This directly addresses the requirements for Category C/C1 of the tender.

**User Persona:** Fleet Manager / Operations Supervisor  
**Core Epic:** Live Fleet Monitoring & Security

### User Stories: Live Tracking and Geofencing

1. **Live Map View:** As an Operations Supervisor, I want to see the real-time location of all my active vehicles on a single, interactive map, so that I can understand the current deployment of my fleet and quickly locate a specific vehicle when needed.

2. **Vehicle Status at a Glance:** As an Operations Supervisor, I want the vehicle icons on the map to be color-coded based on their status (e.g., Green for Moving, Blue for Idling, Red for Stopped/Ignition Off), so that I can instantly understand what each vehicle is doing without having to click on it.

3. **Detailed Vehicle Pop-up:** As an Operations Supervisor, I want to be able to click on any vehicle icon on the map and see a pop-up with key details like driver name, current speed, and registration number, so that I can get immediate context on that specific vehicle's activity.

4. **Geofence Management:** As a Fleet Manager, I want to be able to draw a polygon on the map to create a "geofence" (e.g., around a specific province or construction site), and set rules for it (e.g., alert on entry/exit), so that I can automate the monitoring of vehicle movements in and out of critical or restricted areas.

### Screens: Low-Fidelity Wireframes

For this phase, we will design the main interactive map screen, which serves as the central hub for all live tracking activities.

#### Screen 12: Live Fleet Tracking Map

This is a dynamic screen designed for real-time monitoring.

```
+---------------------------------------------------------------------------------------------------+
| [Company Logo]  Fleet Management System | RT46-2026               [Supervisor Name ▼] [🔔] [Logout] |
|---------------------------------------------------------------------------------------------------|
|  Dashboard   |  Vehicles  |  Maintenance  |  Merchants  | >Tracking< |  Reports  |  Admin          |
+---------------------------------------------------------------------------------------------------+
|                                                                                                   |
|  [ MAP CONTROLS ]                                                                                 |
|  [+] Zoom In  [-] Zoom Out  [Layer: Street/Satellite ▼] [Legend]                                   |
|                                                                                                   |
|  +---------------------------------------------------------+  +---------------------------------+ |
|  |                                                         |  | [ VEHICLE LIST / SEARCH ]       | |
|  |     /``````````\                                        |  |                                 | |
|  |    /     ▲ (Green) \                                    |  | Filter by Status: [All Active ▼]| |
|  |   |               |     +-------------------+         |  | [ Search by Reg/Driver... 🔍 ]  | |
|  |   |           ■(Blue) |   VEHICLE DETAILS   |         |  | ------------------------------- | |
|  |   |                +-------------------+          |  | -> GP 456-XYZ (Moving)          | |
|  |   | ►(Red)          | Reg: GP 456-XYZ   |          |  |    EC 987-CBA (Idling)          | |
|  |   |                | Driver: J. Smith  |          |  |    KZN 111-222 (Stopped)        | |
|  |   \                | Speed: 85 km/h    |          |  |                                 | |
|  |    \_______________/  | Status: Moving    |          |  |                                 | |
|  |      (Map Area)      | [History] [Street View] |         |  |                                 | |
|  |                      +-------------------+         |  |                                 | |
|  |                                                         |  |                                 | |
|  +---------------------------------------------------------+  +---------------------------------+ |
|                                                                                                   |
|  [ EVENT FEED (Live) ]---------------------------------------------------------------------------- |
|  | 10:35:12 - [SPEED ALERT] GP 123-ABC traveling at 135 km/h in a 120 km/h zone.                  |  |
|  | 10:34:50 - [GEOFENCE] KZN 456-DEF has entered the "Durban Port" zone.                         |  |
|  | 10:32:05 - [IGNITION] EC 789-GHI vehicle ignition ON.                                         |  |
|  -------------------------------------------------------------------------------------------------  |
+---------------------------------------------------------------------------------------------------+
```

#### Screen 13: Geofence Creation Tool

This screen would appear as an overlay or a dedicated "edit mode" on the map.

```
+---------------------------------------------------------------------------------------------------+
| ... (Map Interface from Screen 12) ...                                                            |
|                                                                                                   |
|  +---------------------------------------------------------+  +---------------------------------+ |
|  |                                                         |  | [ GEOFENCE EDITOR ]             | |
|  |     /``````````\                                        |  |                                 | |
|  |    /     *----*  \                                      |  | Name* [ Durban Port Zone    ]   | |
|  |   |     /      \  |                                     |  |                                 | |
|  |   |    *        * | <--- (User clicks to draw points)   |  | Alert Rule*                     | |
|  |   |     \      /  |                                     |  | [ On Entry & Exit ▼ ]           | |
|  |   |      *----*   |                                     |  |                                 | |
|  |   \             /                                       |  | Assign to Groups:               | |
|  |    \___________/                                        |  | [x] KZN Fleet  [ ] Durban Metro | |
|  |                                                         |  |                                 | |
|  |                                                         |  |                                 | |
|  |                                                         |  | [ Cancel ] [ SAVE GEOFENCE ]    | |
|  +---------------------------------------------------------+  +---------------------------------+ |
+---------------------------------------------------------------------------------------------------+
```

### Design Rationale

- **Centralized Command Center:** Screen 12 acts as the operational hub, directly supporting User Story 1. The primary real estate is given to the map, with supporting tools arranged logically around it. A searchable list of vehicles provides an alternative way to find an asset that may not be immediately visible on the map.

- **Intuitive Visual Language:** The use of color-coded and shaped icons (User Story 2) allows a supervisor to assess the state of the entire fleet in seconds. A green moving arrow is instantly distinguishable from a red stopped square. This is far more efficient than reading through a table of statuses.

- **On-Demand Detail:** The pop-up box (User Story 3) provides crucial, context-specific information without cluttering the main display. A user can get details when they need them, and hide them when they don't. Including links to "History" or "Street View" provides a clear path to deeper investigation.

- **Real-Time Alerts:** The "Event Feed" at the bottom of the screen is critical. It pushes important information (like speeding or geofence breaches) to the user's attention immediately, without them having to actively search for it.

- **User-Friendly Creation Tools:** The geofence editor (User Story 4) uses a simple, interactive "click-to-draw" model, which is intuitive for users. The form allows for clear rule-setting and assignment, making a powerful feature accessible and easy to manage.

---

## Phase 7: Electronic Inspections

This covers both vehicle and merchant inspections, which are crucial for compliance, safety, and quality assurance.

**User Persona:** Inspector / Field Agent  
**Core Epic:** Conducting Digital Inspections

### User Stories

1. **Guided Inspection Checklist:** As an Inspector, I want to use a mobile app with a guided, digital checklist for a specific inspection type (e.g., "Annual Vehicle Inspection," "New Merchant Onboarding"), so that I can ensure every required point is checked systematically and no steps are missed.

2. **Integrated Evidence Capture:** As an Inspector, while going through the checklist, I want to be able to take photos, add notes, and capture a digital signature directly within the app at the relevant checkpoint, so that all evidence is tied to the specific inspection item it relates to.

3. **Instant Report Generation & Sync:** As an Inspector, upon completing the checklist, I want the app to automatically compile a formal inspection report (PDF) and sync it to the central system under the relevant vehicle or merchant profile, so that the report is immediately available to the Fleet Manager.

### Screen & Design Rationale

#### Screen 14: Mobile Inspection Interface

```
+------------------------------------------+
|  [ < ] Vehicle Inspection: GP 456-XYZ    |
|  ---------------------------------------- |
|  Checklist: Annual Roadworthy           |
|  ---------------------------------------- |
|                                          |
|  [●] 1. Tyres                            |
|      Tread Depth (mm) [ 5.2 ]            |
|      Condition [ Good ▼ ]                |
|      [📷 Take Photo] [📝 Add Note]         |
|                                          |
|  [○] 2. Brakes                           |
|      Visual Check [ Pass / Fail ]        |
|      Test Result [ Good / Poor ]         |
|      [📷 Take Photo] [📝 Add Note]         |
|                                          |
|  [○] 3. Lights & Electrical              |
|                                          |
|  [○] 4. Windscreen & Wipers              |
|                                          |
|  ---------------------------------------- |
|  Inspector Signature:                     |
|  [  John Doe (Digitally Signed) ]         |
|  ---------------------------------------- |
|  [ Save as Draft ] [ COMPLETE & SYNC ]    |
+------------------------------------------+
```

**Design Rationale:** This workflow moves inspections from paper to digital. The mobile-first design is essential for field agents. The guided checklist enforces a standardized process. Tying evidence capture directly to checklist items creates a rich, indisputable record. The "Complete & Sync" button automates the administrative work, saving time and eliminating report backlogs.

---

## Phase 8: Billing & Reporting

This phase addresses how the system aggregates all transactional data and presents it for billing and analysis. This is a high-level administrative function.

**User Persona:** Senior Fleet Manager / Finance Administrator  
**Core Epic:** Financial Oversight and Performance Analysis

### User Stories

1. **Hierarchical Billing:** As a Finance Admin, I want the system to automatically generate a consolidated monthly bill for a high-level entity (e.g., a National Department) but provide a detailed breakdown of costs per sub-entity (e.g., its Provincial offices), so that I can process a single payment while maintaining granular cost allocation.

2. **On-Demand Report Generation:** As a Fleet Manager, I want to access a "Report Builder" where I can select parameters (e.g., date range, vehicle type, department), choose the data I want to see (e.g., maintenance cost, fuel usage, downtime), and generate a custom report in various formats (PDF, XLSX), so that I can perform specific analysis relevant to my current needs.

3. **Secure Document Access & Audit:** As an Auditor, I want to be able to view a transaction (e.g., a specific repair job), and from that screen, click to view all related documentation—the initial request, the quotation, the approval record with timestamp and user, the final invoice, and the inspection report—so that I have a complete, transparent, and easily navigable audit trail.

### Screen & Design Rationale

#### Screen 15: Report Builder Interface

```
+---------------------------------------------------------------------------------------------------+
|  REPORTS > REPORT BUILDER                                                                         |
|  -------------------------------------------------------------------------------------------------  |
|                                                                                                   |
|  1. CHOOSE A REPORT TEMPLATE                                                                      |
|  [ Maintenance Expenditure ▼ ] (Options: Asset Register, Fuel Consumption, etc.)                  |
|                                                                                                   |
|  2. SET PARAMETERS                                                                                |
|  Date Range*  [ 01/06/2025 ] to [ 30/06/2025 ]                                                    |
|  Department*  [ Gauteng > All ▼ ]                                                                 |
|  Vehicle Type [ All ▼ ]                                                                           |
|                                                                                                   |
|  3. CONFIGURE COLUMNS (Drag & Drop)                                                               |
|  [Available Columns]          [Columns in Your Report]                                            |
|  | Vehicle Make   |          | > Vehicle Reg. <   |                                              |
|  | Driver Name    |   <==>   | > Cost Total <     |                                              |
|  | Job ID         |          | > Merchant Name <  |                                              |
|                                                                                                   |
|  4. GENERATE                                                                                      |
|  Output Format: [ PDF ▼ ] (Options: PDF, XLSX, CSV) [ RUN REPORT ] Button                         |
|                                                                                                   |
|  [ SAVED & SCHEDULED REPORTS ]------------------------------------------------------------------  |
|  | "Monthly Over R5k Jobs" - Runs on 1st of month - Emailed to: [<EMAIL>]   [Edit] [Run Now] |
|  -----------------------------------------------------------------------------------------------  |
+---------------------------------------------------------------------------------------------------+
```

**Design Rationale:** This screen provides a powerful self-service reporting tool, fulfilling User Story 2. It allows non-technical users to generate complex reports. The template-driven approach provides easy starting points for common reports (Asset Register, etc.), while the parameter and column configuration offers deep customization. The ability to save and schedule reports automates regular tasks. The underlying data structure, which links all documents to a transaction, naturally facilitates the audit trail access described in User Story 3. The billing requirement (User Story 1) is a back-end logic that would use the department hierarchy defined during setup to roll up costs for invoicing.

---

## Document Information

- **Version:** 1.0
- **Date:** January 2025
- **Author:** Development Team
- **Status:** Active Functional Specification
- **Classification:** Technical Documentation
